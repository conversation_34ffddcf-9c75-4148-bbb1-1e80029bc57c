[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "llie"
version = "0.1.0"
description = "Low-Light Image Enhancement (LLIE) - 基于深度学习的低光图像增强研究框架"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "LLIE Research Team"},
]
keywords = ["computer-vision", "low-light", "image-enhancement", "deep-learning", "pytorch"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Scientific/Engineering :: Image Processing",
]
requires-python = ">=3.9"

# 核心运行时依赖 - 只包含运行模型必需的包
dependencies = [
    # 深度学习核心
    "torch>=1.13.0,<3.0.0",
    "torchvision>=0.14.0,<1.0.0",
    "einops>=0.6.0,<1.0.0",

    # 配置管理
    "hydra-core>=1.3.0,<2.0.0",
    "omegaconf>=2.3.0,<3.0.0",
    "pyyaml>=6.0,<7.0",

    # 日志和进度
    "loguru>=0.7.0,<1.0.0",
    "rich>=13.0.0,<14.0.0",
    "tqdm>=4.64.0,<5.0.0",

    # 图像处理
    "opencv-python>=4.7.0,<5.0.0",
    "Pillow>=9.0.0,<11.0.0",
    "albumentations>=1.3.0,<2.0.0",

    # 科学计算
    "numpy>=1.21.0,<2.0.0",
    "scipy>=1.9.0,<2.0.0",
    "pandas>=1.5.0,<3.0.0",

    # 评估指标
    "lpips>=0.1.4,<1.0.0",
    "pytorch-msssim>=1.0.0,<2.0.0",
    "scikit-image>=0.20.0,<1.0.0",
]

[project.optional-dependencies]
# 开发工具依赖
dev = [
    "ruff>=0.1.0,<1.0.0",          # 现代化的linting和格式化工具，替代black+isort+flake8
    "mypy>=1.0.0,<2.0.0",          # 类型检查
    "pytest>=7.0.0,<9.0.0",       # 测试框架
    "pytest-cov>=4.0.0,<6.0.0",   # 测试覆盖率
    "pre-commit>=3.0.0,<4.0.0",   # Git hooks
]

# Jupyter notebook环境
notebook = [
    "jupyter>=1.0.0,<2.0.0",
    "ipywidgets>=7.6.0,<9.0.0",
]

# 实验跟踪
experiment = [
    "wandb>=0.15.0,<1.0.0",
    "tensorboard>=2.10.0,<3.0.0",
]

# 分布式训练
distributed = [
    "accelerate>=0.20.0,<1.0.0",
]

# 模型分析工具
analysis = [
    "thop>=0.1.1,<1.0.0",         # 模型复杂度分析
    "timm>=0.9.0,<1.0.0",         # 预训练模型库
]

# 可视化
visualization = [
    "matplotlib>=3.5.0,<4.0.0",
    "seaborn>=0.11.0,<1.0.0",
]

# 完整开发环境（包含所有可选依赖）
full = [
    "llie[dev,notebook,experiment,distributed,analysis]",
]

[project.scripts]
llie = "main:main"

[project.urls]
Homepage = "https://github.com/your-org/LLIE"
Repository = "https://github.com/your-org/LLIE"
Documentation = "https://your-org.github.io/LLIE"
"Bug Tracker" = "https://github.com/your-org/LLIE/issues"

# ==================== 工具配置 ====================

[[tool.uv.index]]
url = "https://pypi.tuna.tsinghua.edu.cn/simple"
default = true

[tool.ruff]
# 目标Python版本
target-version = "py39"
line-length = 100
indent-width = 4

# 包含的文件类型
extend-include = ["*.ipynb"]

[tool.ruff.lint]
# 启用的规则集
select = [
    "E",      # pycodestyle errors
    "W",      # pycodestyle warnings
    "F",      # pyflakes
    "I",      # isort
    "B",      # flake8-bugbear
    "C4",     # flake8-comprehensions
    "UP",     # pyupgrade
    "ARG",    # flake8-unused-arguments
    "SIM",    # flake8-simplify
    "TCH",    # flake8-type-checking
    "PTH",    # flake8-use-pathlib
    "ERA",    # eradicate (commented code)
    "PL",     # pylint
    "RUF",    # ruff-specific rules
]

# 忽略的规则
ignore = [
    "E501",   # line-too-long (由formatter处理)
    "B008",   # do-not-perform-function-calls-in-argument-defaults
    "PLR0913", # too-many-arguments
    "PLR0912", # too-many-branches
    "PLR0915", # too-many-statements
    "PLR2004", # magic-value-comparison
]

# 每个文件的忽略规则
[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["F401"]  # 允许在__init__.py中未使用的导入
"tests/**/*.py" = ["ARG", "PLR2004"]  # 测试文件中允许未使用参数和魔法值
"scripts/**/*.py" = ["T201"]  # 脚本中允许print语句

[tool.ruff.lint.isort]
# isort配置
known-first-party = ["llie"]
force-single-line = false
lines-after-imports = 2

[tool.ruff.format]
# 格式化配置
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"

[tool.mypy]
# MyPy类型检查配置
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

# 忽略第三方库的类型检查
[[tool.mypy.overrides]]
module = [
    "cv2.*",
    "albumentations.*",
    "lpips.*",
    "pytorch_msssim.*",
    "thop.*",
    "wandb.*",
    "timm.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
# Pytest配置
minversion = "7.0"
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=src/llie",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
# 覆盖率配置
source = ["src/llie"]
omit = [
    "*/tests/*",
    "*/test_*.py",
    "*/__init__.py",
]

[tool.coverage.report]
# 覆盖率报告配置
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
