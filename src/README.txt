项目整体架构

src/llie/
├── __init__.py          # 包初始化
├── data/               # 数据处理模块
├── engine/             # 训练引擎
├── losses/             # 损失函数
├── models/             # 模型架构
├── tasks/              # 任务管理
└── utils/              # 工具模块
    ├── logging/        # 日志系统
    └── storage/        # 存储管理

核心模块详解

1. 数据模块 (data/)

主要文件：
- dataset.py - 基础数据集抽象类
- lol_dataset.py - LOL 数据集实现
- dataloader.py - 数据加载器工厂
- transforms.py - 数据增强和预处理

工作流程：
# 数据集注册
@register_dataset("LOL")
class LOLDataset(BaseDataset):
    def __init__(self, root_dir, transform=None):
        self.low_light_images = [...]  # 低光图像路径
        self.normal_light_images = [...]  # 正常光图像路径

    def __getitem__(self, idx):
        # 返回 (低光图像, 正常光图像) 对
        return low_img, normal_img

2. 模型架构 (models/)

核心模型：
- base_model.py - 所有模型的基类
- llie.py - DMFourLLIE 主模型实现
- components/ - 可重用的模型组件

DMFourLLIE 四级增强架构：
class DMFourLLIE(BaseArchitecture):
    def __init__(self):
        # 第一级：亮度图处理
        self.luminance_processor = LuminanceMapProcessor()

        # 第二级：傅里叶域增强
        self.fourier_processor = FourierProcessor()

        # 第三级：空间-频率双重处理
        self.dual_processor = DualDomainProcessor()

        # 第四级：最终重建
        self.reconstruction = ReconstructionNetwork()

组件系统：
- luminance_map.py - 亮度图处理
- fourier_blocks.py - 傅里叶变换块
- ffc_blocks.py - 快速傅里叶卷积
- common.py - 通用组件

3. 训练引擎 (engine/)

ModernTrainer 核心功能：
- 混合精度训练支持
- EMA (指数移动平均) 模型
- 早停机制
- 自动模型保存
- 指标监控

class ModernTrainer:
    def __init__(self, model, config):
        self.model = model
        self.optimizer = create_optimizer(config.optimizer)
        self.scheduler = create_scheduler(config.scheduler)
        self.ema_model = EMA(model, decay=0.999)

    def train_epoch(self, dataloader):
        for batch_idx, (low_img, target_img) in enumerate(dataloader):
            # 前向传播
            output = self.model(low_img)

            # 计算损失
            loss = self.criterion(output, target_img)

            # 反向传播
            loss.backward()
            self.optimizer.step()

            # 更新 EMA 模型
            self.ema_model.update()

4. 任务管理 (tasks/)

任务系统架构：
class BaseTask(ABC):
    @abstractmethod
    def run(self, config: DictConfig) -> Any:
        pass

class TrainTask(BaseTask):
    def run(self, config):
        trainer = ModernTrainer(model, config.trainer)
        trainer.fit(dataloader, val_dataloader)

class EvaluateTask(BaseTask):
    def run(self, config):
        results = evaluate_model(model, test_dataloader)
        return results

class InferenceTask(BaseTask):
    def run(self, config):
        enhanced_images = process_images(model, input_paths)
        return enhanced_images

5. 工具模块 (utils/)

注册系统 (registry.py)

# 核心注册机制
MODELS = Registry("models")
LOSSES = Registry("losses")
OPTIMIZERS = Registry("optimizers")

@register_model("LLIE")
class DMFourLLIE(BaseArchitecture):
    pass

# 使用时
model = MODELS.build(config.model)

日志系统 (logging/)

- experiment_logger.py - 实验生命周期日志
- progress_tracker.py - 训练进度可视化
- config_formatter.py - 配置格式化显示

存储管理 (storage/)

- model_manager.py - 模型文件管理
- output_manager.py - 输出目录组织
- result_manager.py - 评估结果管理

系统工作流程

1. 训练流程

# 1. 配置加载
config = HydraConf.load("configs/config.yaml")

# 2. 组件构建
model = MODELS.build(config.model)
dataset = DATASETS.build(config.dataset)
optimizer = OPTIMIZERS.build(config.optimizer)

# 3. 任务执行
task = TrainTask()
task.run(config)

2. 数据流向

原始数据 → 数据加载 → 预处理 → 模型推理 → 损失计算 → 反向传播 → 模型更新
    ↓
保存检查点 → 验证评估 → 指标记录 → 实验跟踪

3. 注册系统工作原理

# 注册阶段
@register_model("my_model")
class MyModel:
    pass

# 使用阶段
model_config = {"type": "my_model", "params": {...}}
model = MODELS.build(model_config)  # 自动实例化

配置驱动架构

Hydra 配置系统

# configs/config.yaml
defaults:
  - task: train
  - model: llie
  - dataset: LOLv2_Real
  - trainer: default_trainer

# 运行时可以覆盖
python main.py task=train trainer.batch_size=16

模块间依赖关系

main.py → tasks/ → models/ → data/
    ↓         ↓        ↓
utils/ → engine/ → losses/
    ↓         ↓
storage/ → logging/

关键设计特点

1. 高度模块化 - 每个组件都有清晰的接口和职责
2. 配置驱动 - 所有行为通过 YAML 配置控制
3. 注册机制 - 支持动态组件替换和扩展
4. 实验管理 - 系统化的实验跟踪和结果管理
5. 现代化工具链 - Loguru、Hydra、W&B、Rich 等工具

这个架构设计使得项目易于扩展、维护和实验，同时保持了良好的代码质量和可读性。

