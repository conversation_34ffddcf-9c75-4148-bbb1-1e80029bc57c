"""
神经网络的通用构建模块。
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional


class ConvBlock(nn.Module):
    """带有归一化和激活的基础卷积块。"""

    def __init__(
        self,
        in_channels: int,
        out_channels: int,
        kernel_size: int = 3,
        stride: int = 1,
        padding: Optional[int] = None,
        bias: bool = True,
        norm: str = "batch",
        activation: str = "leaky_relu",
        **kwargs,
    ):
        super().__init__()

        if padding is None:
            padding = kernel_size // 2

        self.conv = nn.Conv2d(in_channels, out_channels, kernel_size, stride, padding, bias=bias)

        # 归一化层
        if norm == "batch":
            self.norm = nn.BatchNorm2d(out_channels)
        elif norm == "instance":
            self.norm = nn.InstanceNorm2d(out_channels)
        elif norm == "group":
            num_groups = kwargs.get("num_groups", 8)
            self.norm = nn.GroupNorm(num_groups, out_channels)
        else:
            self.norm = nn.Identity()

        # 激活层
        if activation == "relu":
            self.activation = nn.ReLU(inplace=True)
        elif activation == "leaky_relu":
            negative_slope = kwargs.get("negative_slope", 0.1)
            self.activation = nn.LeakyReLU(negative_slope, inplace=True)
        elif activation == "gelu":
            self.activation = nn.GELU()
        elif activation == "swish":
            self.activation = nn.SiLU()
        else:
            self.activation = nn.Identity()

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x = self.conv(x)
        x = self.norm(x)
        x = self.activation(x)
        return x


class ResidualBlock(nn.Module):
    """带有跳跃连接的残差块。"""

    def __init__(
        self,
        channels: int,
        kernel_size: int = 3,
        stride: int = 1,
        use_bias: bool = True,
        norm: str = "batch",
        activation: str = "leaky_relu",
        **kwargs,
    ):
        super().__init__()

        self.conv1 = ConvBlock(
            channels,
            channels,
            kernel_size,
            stride,
            bias=use_bias,
            norm=norm,
            activation=activation,
            **kwargs,
        )

        self.conv2 = ConvBlock(
            channels, channels, kernel_size, 1, bias=use_bias, norm=norm, activation="none"
        )

        # 最终激活层
        if activation == "relu":
            self.final_activation = nn.ReLU(inplace=True)
        elif activation == "leaky_relu":
            negative_slope = kwargs.get("negative_slope", 0.1)
            self.final_activation = nn.LeakyReLU(negative_slope, inplace=True)
        else:
            self.final_activation = nn.Identity()

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        residual = x
        out = self.conv1(x)
        out = self.conv2(out)
        out = out + residual
        out = self.final_activation(out)
        return out


class UpSample(nn.Module):
    """使用不同方法的上采样模块。"""

    def __init__(
        self,
        in_channels: int,
        out_channels: int,
        scale_factor: int = 2,
        mode: str = "transpose_conv",
        **kwargs,
    ):
        super().__init__()

        self.scale_factor = scale_factor
        self.mode = mode

        if mode == "transpose_conv":
            self.upsample = nn.ConvTranspose2d(
                in_channels, out_channels, kernel_size=scale_factor, stride=scale_factor
            )
        elif mode == "pixel_shuffle":
            self.conv = nn.Conv2d(in_channels, out_channels * (scale_factor**2), 1)
            self.upsample = nn.PixelShuffle(scale_factor)
        elif mode == "bilinear":
            self.upsample = nn.Sequential(
                nn.Upsample(scale_factor=scale_factor, mode="bilinear", align_corners=False),
                nn.Conv2d(in_channels, out_channels, 1),
            )
        else:
            raise ValueError(f"Unknown upsample mode: {mode}")

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        if self.mode == "pixel_shuffle":
            x = self.conv(x)
        return self.upsample(x)


class DownSample(nn.Module):
    """使用不同方法的下采样模块。"""

    def __init__(
        self,
        in_channels: int,
        out_channels: int,
        scale_factor: int = 2,
        mode: str = "conv",
        **kwargs,
    ):
        super().__init__()

        if mode == "conv":
            self.downsample = nn.Conv2d(
                in_channels, out_channels, kernel_size=scale_factor, stride=scale_factor
            )
        elif mode == "pool":
            self.downsample = nn.Sequential(
                nn.MaxPool2d(scale_factor), nn.Conv2d(in_channels, out_channels, 1)
            )
        elif mode == "avgpool":
            self.downsample = nn.Sequential(
                nn.AvgPool2d(scale_factor), nn.Conv2d(in_channels, out_channels, 1)
            )
        else:
            raise ValueError(f"Unknown downsample mode: {mode}")

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return self.downsample(x)


class MultiScaleConv(nn.Module):
    """多尺度卷积模块。"""

    def __init__(
        self, in_channels: int, out_channels: int, kernel_sizes: list = [1, 3, 5, 7], **kwargs
    ):
        super().__init__()

        assert len(kernel_sizes) > 0

        # 每个分支有 out_channels // len(kernel_sizes) 个通道
        branch_channels = out_channels // len(kernel_sizes)

        self.branches = nn.ModuleList(
            [ConvBlock(in_channels, branch_channels, kernel_size=k, **kwargs) for k in kernel_sizes]
        )

        # 如果通道数不能整除，调整最后一个分支
        remaining_channels = out_channels - branch_channels * (len(kernel_sizes) - 1)
        if remaining_channels != branch_channels:
            self.branches[-1] = ConvBlock(
                in_channels, remaining_channels, kernel_size=kernel_sizes[-1], **kwargs
            )

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        outputs = [branch(x) for branch in self.branches]
        return torch.cat(outputs, dim=1)


class SpatialAttention(nn.Module):
    """空间注意力机制。"""

    def __init__(self, kernel_size: int = 7):
        super().__init__()

        self.conv = nn.Conv2d(2, 1, kernel_size, padding=kernel_size // 2, bias=False)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # 计算空间统计信息
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)

        # 拼接并处理
        attention_input = torch.cat([avg_out, max_out], dim=1)
        attention = self.conv(attention_input)
        attention = self.sigmoid(attention)

        return x * attention


class AdaptiveInstanceNorm(nn.Module):
    """自适应实例归一化。"""

    def __init__(self, num_features: int):
        super().__init__()
        self.num_features = num_features
        self.weight = nn.Parameter(torch.ones(num_features))
        self.bias = nn.Parameter(torch.zeros(num_features))
        self.eps = 1e-5

    def forward(self, x: torch.Tensor, style: torch.Tensor) -> torch.Tensor:
        # x: [B, C, H, W] 输入特征图
        # style: [B, C] 或 [B, C, 1, 1] 风格特征

        B, C = x.size(0), x.size(1)

        # 计算实例统计信息
        x_reshaped = x.view(B, C, -1)
        mean = x_reshaped.mean(dim=2, keepdim=True)
        var = x_reshaped.var(dim=2, keepdim=True)

        # 归一化处理
        x_norm = (x_reshaped - mean) / torch.sqrt(var + self.eps)

        # 应用风格
        if style.dim() == 2:
            style = style.unsqueeze(2)

        style_mean, style_var = style.chunk(2, dim=1)

        out = x_norm * style_var + style_mean
        return out.view_as(x)


class SEBlock(nn.Module):
    """压缩-激励块。"""

    def __init__(self, channels: int, reduction: int = 16):
        super().__init__()
        self.global_pool = nn.AdaptiveAvgPool2d(1)
        self.fc = nn.Sequential(
            nn.Linear(channels, channels // reduction, bias=False),
            nn.ReLU(inplace=True),
            nn.Linear(channels // reduction, channels, bias=False),
            nn.Sigmoid(),
        )

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        b, c, _, _ = x.size()
        y = self.global_pool(x).view(b, c)
        y = self.fc(y).view(b, c, 1, 1)
        return x * y.expand_as(x)


def make_layer(block, n_layers, **kwargs):
    """创建一个块序列。"""
    layers = []
    for _ in range(n_layers):
        layers.append(block(**kwargs))
    return nn.Sequential(*layers)


class ResidualBlock_noBN(nn.Module):
    """无批归一化的残差块。"""

    def __init__(self, nf: int = 64):
        super().__init__()
        self.conv1 = nn.Conv2d(nf, nf, 3, 1, 1, bias=True)
        self.conv2 = nn.Conv2d(nf, nf, 3, 1, 1, bias=True)
        self.activation = nn.ReLU(inplace=True)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        identity = x
        out = self.activation(self.conv1(x))
        out = self.conv2(out)
        return out + identity


class ChannelAttentionFusion(nn.Module):
    """通道注意力融合模块。"""

    def __init__(self, channels: int, reduction: int = 16):
        super().__init__()
        self.global_pool = nn.AdaptiveAvgPool2d(1)
        self.fc1 = nn.Conv2d(channels * 2, channels // reduction, 1, bias=False)
        self.fc2 = nn.Conv2d(channels // reduction, channels * 2, 1, bias=False)
        self.activation = nn.ReLU(inplace=True)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x1: torch.Tensor, x2: torch.Tensor) -> torch.Tensor:
        # 拼接输入
        x_cat = torch.cat([x1, x2], dim=1)

        # 全局平均池化
        x_gap = self.global_pool(x_cat)

        # 通道注意力
        x_att = self.activation(self.fc1(x_gap))
        x_att = self.sigmoid(self.fc2(x_att))

        # 分割注意力权重
        att1, att2 = x_att.chunk(2, dim=1)

        # 应用注意力并融合
        return x1 * att1 + x2 * att2
