"""
HVI色彩空间转换模块

本模块实现了RGB与HVI色彩空间之间的双向转换功能。HVI色彩空间是一种专门为
低光图像增强设计的色彩表示方法，具有以下特点：

- H通道：色调的余弦分量，范围[-1, 1]
- V通道：色调的正弦分量，范围[-1, 1]  
- I通道：强度/亮度，范围[0, 1]

HVI色彩空间的优势：
1. 更好的色彩-亮度分离：H和V通道主要包含色彩信息，I通道包含亮度信息
2. 低光增强友好：特别适合低光图像增强任务
3. 色彩敏感度调整：通过参数k可以调整色彩敏感度
4. 可微分：支持端到端训练

主要组件：
- HVITransform: 核心的RGB↔HVI双向转换类
- 支持批量处理和GPU加速
- 包含完整的参数配置和错误处理

使用示例：
    >>> transform = HVITransform()
    >>> rgb_image = torch.randn(4, 3, 256, 256)  # [B, C, H, W]
    >>> hvi_image = transform.rgb_to_hvi(rgb_image)
    >>> reconstructed_rgb = transform.hvi_to_rgb(hvi_image)
"""

import math
from typing import Tuple, Optional, Union
import torch
import torch.nn as nn
from loguru import logger

from ...utils.registry import register_model


# 数学常数
PI = math.pi


@register_model("HVITransform")
class HVITransform(nn.Module):
    """
    HVI色彩空间转换器
    
    实现RGB与HVI色彩空间之间的双向转换。HVI是一种专门为低光图像增强
    设计的色彩空间，能够更好地分离色彩和亮度信息。
    
    转换原理：
    1. RGB → HSV：首先转换到HSV色彩空间
    2. HSV → HVI：应用色彩敏感度变换
       - H = color_sensitive * saturation * cos(2π * hue)
       - V = color_sensitive * saturation * sin(2π * hue)  
       - I = value (亮度保持不变)
    3. color_sensitive = (sin(value * π/2) + eps)^k
    
    参数说明：
        density_k: 色彩敏感度参数，控制色彩在不同亮度下的表现
                  较小的k值使暗部色彩更敏感，较大的k值使亮部色彩更敏感
        eps: 数值稳定性参数，防止除零错误
        learnable_k: 是否将density_k设为可学习参数
        
    输入输出格式：
        输入: RGB图像 [B, 3, H, W]，值域[0, 1]
        输出: HVI图像 [B, 3, H, W]，H,V∈[-1,1], I∈[0,1]
    """
    
    def __init__(
        self,
        density_k: float = 0.2,
        eps: float = 1e-8,
        learnable_k: bool = True,
        **kwargs
    ):
        """
        初始化HVI色彩空间转换器
        
        参数:
            density_k: 色彩敏感度参数，默认0.2
            eps: 数值稳定性参数，默认1e-8  
            learnable_k: 是否将density_k设为可学习参数，默认True
            **kwargs: 其他参数（用于兼容性）
        """
        super().__init__()
        
        self.eps = eps
        
        # 色彩敏感度参数
        if learnable_k:
            self.density_k = nn.Parameter(torch.tensor(density_k))
        else:
            self.register_buffer('density_k', torch.tensor(density_k))
            
        # 用于存储当前k值（用于逆转换）
        self.register_buffer('current_k', torch.tensor(density_k))
        
        logger.info(f"初始化HVI色彩空间转换器: density_k={density_k}, learnable_k={learnable_k}")
    
    def rgb_to_hvi(self, rgb: torch.Tensor) -> torch.Tensor:
        """
        将RGB图像转换为HVI色彩空间
        
        转换步骤：
        1. RGB → HSV色彩空间转换
        2. 计算色彩敏感度函数
        3. 应用HVI变换公式
        
        参数:
            rgb: RGB图像张量 [B, 3, H, W]，值域[0, 1]
            
        返回:
            HVI图像张量 [B, 3, H, W]，H,V∈[-1,1], I∈[0,1]
            
        异常:
            ValueError: 当输入张量格式不正确时
            RuntimeError: 当计算过程中出现数值错误时
        """
        if rgb.dim() != 4 or rgb.size(1) != 3:
            raise ValueError(f"期望输入形状为[B, 3, H, W]，实际得到{rgb.shape}")
            
        if rgb.min() < 0 or rgb.max() > 1:
            logger.warning(f"RGB值超出[0,1]范围: min={rgb.min():.3f}, max={rgb.max():.3f}")
            rgb = torch.clamp(rgb, 0, 1)
        
        device = rgb.device
        dtype = rgb.dtype
        batch_size, _, height, width = rgb.shape
        
        try:
            # 1. RGB到HSV转换
            ## 1.1 计算value (最大值) 和 min值
            value, _ = rgb.max(dim=1, keepdim=False)  # [B, H, W]
            min_val, _ = rgb.min(dim=1, keepdim=False)  # [B, H, W]
            
            # 初始化hue张量
            hue = torch.zeros(batch_size, height, width, device=device, dtype=dtype)
            
            ## 1.2 计算色调 (hue)
            # 当B通道为最大值时
            mask_b = (rgb[:, 2] == value)
            if mask_b.any():
                hue[mask_b] = 4.0 + (rgb[:, 0] - rgb[:, 1])[mask_b] / (value - min_val + self.eps)[mask_b]
            
            # 当G通道为最大值时  
            mask_g = (rgb[:, 1] == value)
            if mask_g.any():
                hue[mask_g] = 2.0 + (rgb[:, 2] - rgb[:, 0])[mask_g] / (value - min_val + self.eps)[mask_g]
            
            # 当R通道为最大值时
            mask_r = (rgb[:, 0] == value)
            if mask_r.any():
                hue[mask_r] = ((rgb[:, 1] - rgb[:, 2]) / (value - min_val + self.eps))[mask_r] % 6
            
            # 处理灰度像素（所有通道值相等）
            gray_mask = (min_val == value)
            hue[gray_mask] = 0.0
            
            # 归一化hue到[0, 1] (这也就是造成红色伪影的原因)
            hue = hue / 6.0
            
            ## 1.3 计算饱和度 (saturation)
            saturation = (value - min_val) / (value + self.eps)
            saturation[value == 0] = 0
            
            # 第二步：HSV到HVI转换
            # 更新当前k值
            k = self.density_k
            self.current_k.copy_(k)
            
            # 计算色彩敏感度函数
            color_sensitive = ((value * 0.5 * PI).sin() + self.eps).pow(k)
            
            # 计算HVI通道
            ch = (2.0 * PI * hue).cos()  # 色调余弦分量
            cv = (2.0 * PI * hue).sin()  # 色调正弦分量
            
            H = color_sensitive * saturation * ch
            V = color_sensitive * saturation * cv
            I = value
            
            # 组合HVI通道
            hvi = torch.stack([H, V, I], dim=1)  # [B, 3, H, W]
            
            return hvi
            
        except Exception as e:
            logger.error(f"RGB到HVI转换失败: {e}")
            raise RuntimeError(f"RGB到HVI转换过程中发生错误: {e}")

    def hvi_to_rgb(self, hvi: torch.Tensor) -> torch.Tensor:
        """
        将HVI图像转换回RGB色彩空间

        逆转换步骤：
        1. 从HVI提取H、V、I通道
        2. 逆向计算HSV色彩空间
        3. HSV → RGB色彩空间转换

        参数:
            hvi: HVI图像张量 [B, 3, H, W]，H,V∈[-1,1], I∈[0,1]

        返回:
            RGB图像张量 [B, 3, H, W]，值域[0, 1]

        异常:
            ValueError: 当输入张量格式不正确时
            RuntimeError: 当计算过程中出现数值错误时
        """
        if hvi.dim() != 4 or hvi.size(1) != 3:
            raise ValueError(f"期望输入形状为[B, 3, H, W]，实际得到{hvi.shape}")

        try:
            # 分离HVI通道
            H, V, I = hvi[:, 0], hvi[:, 1], hvi[:, 2]  # [B, H, W]

            # 限制数值范围
            H = torch.clamp(H, -1, 1)
            V = torch.clamp(V, -1, 1)
            I = torch.clamp(I, 0, 1)

            # 第一步：HVI到HSV逆转换
            value = I
            k = self.current_k.item()

            # 逆向计算色彩敏感度
            color_sensitive = ((value * 0.5 * PI).sin() + self.eps).pow(k)

            # 逆向计算饱和度分量
            H_norm = H / (color_sensitive + self.eps)
            V_norm = V / (color_sensitive + self.eps)
            H_norm = torch.clamp(H_norm, -1, 1)
            V_norm = torch.clamp(V_norm, -1, 1)

            # 计算色调和饱和度
            hue = torch.atan2(V_norm + self.eps, H_norm + self.eps) / (2 * PI)
            hue = hue % 1  # 确保在[0, 1]范围内

            saturation = torch.sqrt(H_norm**2 + V_norm**2 + self.eps)
            saturation = torch.clamp(saturation, 0, 1)

            # 第二步：HSV到RGB转换
            device = hvi.device
            dtype = hvi.dtype

            # 初始化RGB通道
            r = torch.zeros_like(hue)
            g = torch.zeros_like(hue)
            b = torch.zeros_like(hue)

            # HSV到RGB转换算法
            h_i = torch.floor(hue * 6.0)
            f = hue * 6.0 - h_i
            p = value * (1.0 - saturation)
            q = value * (1.0 - f * saturation)
            t = value * (1.0 - (1.0 - f) * saturation)

            # 根据色调区间分配RGB值
            # 区间0: h ∈ [0, 1)
            mask0 = (h_i == 0)
            r[mask0] = value[mask0]
            g[mask0] = t[mask0]
            b[mask0] = p[mask0]

            # 区间1: h ∈ [1, 2)
            mask1 = (h_i == 1)
            r[mask1] = q[mask1]
            g[mask1] = value[mask1]
            b[mask1] = p[mask1]

            # 区间2: h ∈ [2, 3)
            mask2 = (h_i == 2)
            r[mask2] = p[mask2]
            g[mask2] = value[mask2]
            b[mask2] = t[mask2]

            # 区间3: h ∈ [3, 4)
            mask3 = (h_i == 3)
            r[mask3] = p[mask3]
            g[mask3] = q[mask3]
            b[mask3] = value[mask3]

            # 区间4: h ∈ [4, 5)
            mask4 = (h_i == 4)
            r[mask4] = t[mask4]
            g[mask4] = p[mask4]
            b[mask4] = value[mask4]

            # 区间5: h ∈ [5, 6)
            mask5 = (h_i == 5)
            r[mask5] = value[mask5]
            g[mask5] = p[mask5]
            b[mask5] = q[mask5]

            # 组合RGB通道
            rgb = torch.stack([r, g, b], dim=1)  # [B, 3, H, W]

            # 确保输出在有效范围内
            rgb = torch.clamp(rgb, 0, 1)

            return rgb

        except Exception as e:
            logger.error(f"HVI到RGB转换失败: {e}")
            raise RuntimeError(f"HVI到RGB转换过程中发生错误: {e}")

    def forward(self, x: torch.Tensor, mode: str = "rgb_to_hvi") -> torch.Tensor:
        """
        前向传播方法，支持双向转换

        参数:
            x: 输入图像张量
            mode: 转换模式，"rgb_to_hvi" 或 "hvi_to_rgb"

        返回:
            转换后的图像张量
        """
        if mode == "rgb_to_hvi":
            return self.rgb_to_hvi(x)
        elif mode == "hvi_to_rgb":
            return self.hvi_to_rgb(x)
        else:
            raise ValueError(f"不支持的转换模式: {mode}，支持的模式: 'rgb_to_hvi', 'hvi_to_rgb'")

    def get_config(self) -> dict:
        """
        获取模型配置用于序列化

        返回:
            包含模型关键参数的配置字典
        """
        return {
            "type": "HVITransform",
            "density_k": self.density_k.item() if hasattr(self.density_k, 'item') else self.density_k,
            "eps": self.eps,
            "learnable_k": isinstance(self.density_k, nn.Parameter),
        }


# 便利函数：提供函数式接口
def rgb_to_hvi(
    rgb: torch.Tensor,
    density_k: float = 0.2,
    eps: float = 1e-8
) -> torch.Tensor:
    """
    函数式RGB到HVI转换接口

    参数:
        rgb: RGB图像张量 [B, 3, H, W]
        density_k: 色彩敏感度参数
        eps: 数值稳定性参数

    返回:
        HVI图像张量 [B, 3, H, W]
    """
    transform = HVITransform(density_k=density_k, eps=eps, learnable_k=False)
    transform.eval()
    with torch.no_grad():
        return transform.rgb_to_hvi(rgb)


def hvi_to_rgb(
    hvi: torch.Tensor,
    density_k: float = 0.2,
    eps: float = 1e-8
) -> torch.Tensor:
    """
    函数式HVI到RGB转换接口

    参数:
        hvi: HVI图像张量 [B, 3, H, W]
        density_k: 色彩敏感度参数（需与正向转换时一致）
        eps: 数值稳定性参数

    返回:
        RGB图像张量 [B, 3, H, W]
    """
    transform = HVITransform(density_k=density_k, eps=eps, learnable_k=False)
    transform.eval()
    with torch.no_grad():
        return transform.hvi_to_rgb(hvi)


