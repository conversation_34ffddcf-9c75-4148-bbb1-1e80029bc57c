"""
亮度图处理组件

本模块提供了低光图像增强的亮度图处理功能，包括：
- 基于U-Net架构的编码器-解码器结构
- 多尺度特征处理和跳跃连接
- 亮度增强图的生成

亮度图处理是LLIE架构的第一阶段，负责从输入图像中提取
和处理亮度信息，为后续的傅里叶域增强提供照明指导。

主要组件：
- BasicConv: 基础卷积块
- DownScale: 下采样模块
- UpScale: 上采样模块  
- LuminanceMapProcessor: 亮度图处理器（核心组件）
"""

import torch
import torch.nn as nn
from typing import List, Tuple
from ...utils.registry import register_model


class BasicConv(nn.Module):
    """
    基础卷积块

    这是一个灵活的基础构建块，可以根据不同用途进行配置，
    包括用于上采样的转置卷积。是亮度图处理器的基本组成单元。

    参数:
        in_channels: 输入通道数
        out_channels: 输出通道数
        kernel_size: 卷积核大小
        stride: 卷积步长
        padding: 卷积填充。如果为None，自动计算为 kernel_size // 2
        bias: 是否使用偏置。使用归一化时自动禁用
        norm: 是否使用实例归一化
        activation: 是否使用ReLU激活
        transpose: 是否使用转置卷积进行上采样

    特点:
        - 支持普通卷积和转置卷积
        - 可选的归一化和激活
        - 自动填充计算
        - 灵活的配置选项
    """

    def __init__(
        self,
        in_channels: int,
        out_channels: int,
        kernel_size: int = 3,
        stride: int = 1,
        padding: int = None,
        bias: bool = True,
        norm: bool = False,
        activation: bool = True,
        transpose: bool = False,
    ):
        super().__init__()

        # Disable bias if using normalization
        if bias and norm:
            bias = False

        # Compute padding if not specified
        if padding is None:
            if transpose:
                padding = kernel_size // 2 - 1
            else:
                padding = kernel_size // 2

        # Build layers
        layers = []

        if transpose:
            layers.append(
                nn.ConvTranspose2d(
                    in_channels,
                    out_channels,
                    kernel_size,
                    padding=padding,
                    stride=stride,
                    bias=bias,
                )
            )
        else:
            layers.append(
                nn.Conv2d(
                    in_channels,
                    out_channels,
                    kernel_size,
                    padding=padding,
                    stride=stride,
                    bias=bias,
                )
            )

        if norm:
            layers.append(nn.InstanceNorm2d(out_channels))

        if activation:
            layers.append(nn.ReLU(inplace=True))

        self.main = nn.Sequential(*layers)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播通过卷积块

        参数:
            x: 输入张量 [B, C, H, W]

        返回:
            处理后的张量 [B, C', H', W']
        """
        return self.main(x)


class DownScale(nn.Module):
    """
    下采样模块

    使用步长为2的卷积进行下采样操作，在降低空间分辨率的同时
    增加通道数量，这是U-Net架构编码器的标准操作。

    工作原理:
    - 输入: [B, C, H, W]
    - 输出: [B, C*2, H/2, W/2]
    - 使用3x3卷积，步长为2
    - 保持特征图的空间信息同时增加通道深度
    """

    def __init__(self, in_channels: int):
        super().__init__()
        self.main = BasicConv(in_channels, in_channels * 2, 3, 2)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return self.main(x)


class UpScale(nn.Module):
    """
    上采样模块

    使用转置卷积进行上采样操作，在增加空间分辨率的同时
    减少通道数量，这是U-Net架构解码器的标准操作。

    工作原理:
    - 输入: [B, C, H, W]
    - 输出: [B, C/2, H*2, W*2]
    - 使用4x4转置卷积，步长为2
    - 与下采样模块相对应，恢复空间分辨率
    """

    def __init__(self, in_channels: int):
        super().__init__()
        self.main = BasicConv(
            in_channels, in_channels // 2, kernel_size=4, stride=2, transpose=True
        )

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return self.main(x)


@register_model("LuminanceMapProcessor")
class LuminanceMapProcessor(nn.Module):
    """
    亮度图处理器 - 基于U-Net架构的亮度处理网络

    这是LLIE架构第一阶段的核心组件，负责处理亮度通道并生成增强图，
    为整个低光增强过程提供照明指导。采用编码器-解码器架构和跳跃连接，
    实现多尺度特征处理。

    架构特点:
    - 4尺度处理：从原始分辨率到1/16分辨率
    - 跳跃连接：保持高分辨率细节信息
    - 对称结构：编码器和解码器通道数对称变化
    - 实例归一化：提高训练稳定性

    处理流程:
    1. 输入投影：将输入映射到基础通道数
    2. 编码器：逐步下采样，提取多尺度特征
    3. 瓶颈层：在最小尺度上进行深度特征处理
    4. 解码器：逐步上采样，融合跳跃连接特征
    5. 输出投影：生成最终的增强图

    参数:
        depth: 整数列表，指定每个尺度的处理深度
              默认: [1, 1, 1, 1] 表示4个尺度
        base_channels: 基础通道数，每次下采样翻倍
        input_channels: 输入通道数（通常为1，亮度图）
        output_channels: 输出通道数（通常为1，增强图）

    示例:
        >>> processor = LuminanceMapProcessor(depth=[2, 2, 2, 2], base_channels=32)
        >>> luminance = torch.randn(4, 1, 256, 256)  # 亮度图像批次
        >>> enhancement_map = processor(luminance)   # 增强后的亮度图

    网络规模:
        - 输入尺寸: [B, 1, H, W]
        - 输出尺寸: [B, 1, H, W]
        - 参数量: 取决于depth和base_channels配置
        - 计算复杂度: O(H*W*C) 其中C为通道数
    """

    def __init__(
        self,
        depth: List[int] = [1, 1, 1, 1],
        base_channels: int = 16,
        input_channels: int = 1,
        output_channels: int = 1,
    ):
        super().__init__()

        self.depth = depth
        self.base_channels = base_channels

        # Input projection
        self.conv_first = BasicConv(input_channels, base_channels, 3, 1)

        # Encoder: 4 scales with progressively more channels
        self.encoder = nn.ModuleList()
        current_channels = base_channels

        for i, d in enumerate(depth[:-1]):  # Exclude the last depth (bottleneck)
            # Feature processing at current scale
            self.encoder.append(BasicConv(current_channels, current_channels, 3, 1))
            self.encoder.append(
                nn.Sequential(
                    *[BasicConv(current_channels, current_channels, 3, 1) for _ in range(d)]
                )
            )

            # Downsampling
            self.encoder.append(DownScale(current_channels))
            current_channels *= 2

        # Bottleneck (middle processing)
        self.middle = nn.Sequential(
            *[BasicConv(current_channels, current_channels, 3, 1) for _ in range(depth[-1])]
        )

        # Decoder: 4 scales with progressively fewer channels
        self.decoder = nn.ModuleList()

        for i, d in enumerate(reversed(depth[:-1])):
            # Upsampling
            self.decoder.append(UpScale(current_channels))
            current_channels //= 2

            # Feature fusion (after concatenation with skip connection)
            # Note: channels doubled due to skip connection concatenation
            self.decoder.append(BasicConv(current_channels * 2, current_channels, 3, 1))

            # Feature processing at current scale
            self.decoder.append(
                nn.Sequential(
                    *[BasicConv(current_channels, current_channels, 3, 1) for _ in range(d)]
                )
            )

        # Output projection
        self.conv_last = nn.Conv2d(base_channels, output_channels, 3, 1, 1)

        # Initialize weights
        self._initialize_weights()

    def _initialize_weights(self):
        """
        使用现代最佳实践初始化网络权重

        权重初始化策略：
        - 卷积层：使用Kaiming正态分布初始化，适配ReLU激活函数
        - 转置卷积：同样使用Kaiming初始化
        - 偏置：初始化为0
        - 实例归一化：权重初始化为1，偏置初始化为0
        """
        for module in self.modules():
            if isinstance(module, (nn.Conv2d, nn.ConvTranspose2d)):
                nn.init.kaiming_normal_(module.weight, mode="fan_out", nonlinearity="relu")
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)
            elif isinstance(module, nn.InstanceNorm2d):
                nn.init.constant_(module.weight, 1)
                nn.init.constant_(module.bias, 0)

    def encode(self, x: torch.Tensor) -> Tuple[torch.Tensor, List[torch.Tensor]]:
        """
        编码器前向传播并收集跳跃连接

        执行编码器的前向传播过程，逐步下采样并收集用于跳跃连接的特征图。

        处理步骤:
        1. 对每个编码器层进行处理
        2. 在下采样前收集跳跃连接特征
        3. 返回最终编码特征和跳跃连接列表

        参数:
            x: 输入张量 [B, C, H, W]

        返回:
            元组 (编码特征, 跳跃连接列表)
            - 编码特征: 最小尺度的特征表示
            - 跳跃连接: 各个尺度的特征图，用于解码器融合
        """
        shortcuts = []

        for i in range(len(self.encoder)):
            x = self.encoder[i](x)

            # Collect skip connections before downsampling
            if (i + 2) % 3 == 0:  # After every [conv, blocks, downsample] group
                shortcuts.append(x)

        return x, shortcuts

    def decode(self, x: torch.Tensor, shortcuts: List[torch.Tensor]) -> torch.Tensor:
        """
        解码器前向传播并融合跳跃连接

        执行解码器的前向传播过程，逐步上采样并融合来自编码器的跳跃连接特征。

        处理步骤:
        1. 对每个解码器层进行处理
        2. 在上采样后融合对应的跳跃连接特征
        3. 通过通道拼接实现特征融合
        4. 返回解码后的特征图

        参数:
            x: 编码特征 [B, C, H, W]
            shortcuts: 跳跃连接特征列表

        返回:
            解码后的特征 [B, C, H*2^N, W*2^N]，其中N为解码器层数
        """
        for i in range(len(self.decoder)):
            # Fuse skip connections after upsampling
            if (i + 2) % 3 == 0:  # After every [upsample, fusion, blocks] group
                skip_index = len(shortcuts) - (i // 3 + 1)
                x = torch.cat([x, shortcuts[skip_index]], dim=1)

            x = self.decoder[i](x)

        return x

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        亮度图处理器的前向传播

        执行完整的U-Net架构前向传播，生成增强后的亮度图：
        1. 输入投影：将输入映射到合适的通道空间
        2. 编码处理：提取多尺度特征并收集跳跃连接
        3. 瓶颈处理：在最小尺度上进行深度特征提取
        4. 解码处理：上采样并融合跳跃连接特征
        5. 输出投影：生成最终的增强图并应用sigmoid激活

        参数:
            x: 输入亮度图像 [B, 1, H, W]，值域[0, 1]

        返回:
            增强后的亮度图 [B, 1, H, W]，值域[0, 1]
            包含照明增强信息的指导图
        """
        # Input projection
        x = self.conv_first(x)

        # Encoder-decoder processing
        x, shortcuts = self.encode(x)
        x = self.middle(x)
        x = self.decode(x, shortcuts)

        # Output projection with sigmoid activation
        x = self.conv_last(x)
        y = torch.sigmoid(x)

        return y

    def get_config(self) -> dict:
        """
        获取模型配置用于序列化

        返回:
            包含模型关键参数的配置字典，用于模型保存和加载
        """
        return {
            "type": "LuminanceMapProcessor",
            "depth": self.depth,
            "base_channels": self.base_channels,
        }


# Legacy alias for backward compatibility
LuminaceMap = LuminanceMapProcessor
