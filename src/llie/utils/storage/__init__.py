"""
存储管理模块

提供智能的文件存储和管理功能，优化存储空间使用。

主要组件：
- OutputManager: 输出文件管理器，负责整体的文件组织和存储策略
- ModelManager: 模型文件管理器，负责模型检查点和最优模型的管理
- ResultManager: 结果存储管理器，负责评估结果和图片样例的存储

设计原则：
1. 空间优化：避免重复存储，使用软链接管理
2. 清晰组织：文件按类型和重要性分类
3. 易于维护：自动清理和更新机制
4. 配置灵活：支持存储策略的自定义配置
"""

from .output_manager import OutputManager
from .model_manager import ModelManager
from .result_manager import ResultManager

__all__ = ["OutputManager", "ModelManager", "ResultManager"]
