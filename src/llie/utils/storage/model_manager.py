"""
模型文件管理器

负责模型检查点的存储、最优模型的链接管理和模型文件的清理。
"""

import os
import shutil
from pathlib import Path
from typing import Dict, Any, Optional, List
from loguru import logger


class ModelManager:
    """
    模型文件管理器

    主要功能：
    1. 管理训练检查点的保存和清理
    2. 维护全局最优模型的软链接
    3. 根据评估指标自动更新最优模型
    4. 提供模型文件的查找和恢复功能

    存储策略：
    - 每个实验只保留最终模型和最新检查点
    - 全局维护各指标的最优模型软链接
    - 自动清理过期的检查点文件
    """

    def __init__(self, base_output_dir: str = "./outputs"):
        """
        初始化模型管理器

        参数:
            base_output_dir: 基础输出目录
        """
        self.base_output_dir = Path(base_output_dir)

        # 创建全局模型目录结构
        self.global_models_dir = self.base_output_dir / "models"
        self.best_models_dir = self.global_models_dir / "best_models"

        # 创建目录
        self.global_models_dir.mkdir(parents=True, exist_ok=True)
        self.best_models_dir.mkdir(parents=True, exist_ok=True)

        # 全局最优模型记录文件（用于向后兼容）
        self.global_best_models_record = self.best_models_dir / "best_models_info.json"

        # 执行数据迁移（如果需要）
        self._migrate_global_records_to_dataset_specific()

        logger.info(f"模型管理器初始化完成: {self.global_models_dir}")

    def save_checkpoint(
        self,
        checkpoint_data: Dict[str, Any],
        experiment_dir: Path,
        epoch: int,
        is_final: bool = False,
    ) -> Path:
        """
        保存模型检查点

        参数:
            checkpoint_data: 检查点数据
            experiment_dir: 实验目录
            epoch: 训练轮次
            is_final: 是否为最终模型

        返回:
            保存的检查点路径
        """
        models_dir = experiment_dir / "models"
        models_dir.mkdir(parents=True, exist_ok=True)

        if is_final:
            checkpoint_path = models_dir / "final_model.pth"
        else:
            checkpoint_path = models_dir / f"checkpoint_epoch_{epoch:03d}.pth"

        # 保存检查点
        import torch

        torch.save(checkpoint_data, checkpoint_path)

        # 同时保存为最新检查点
        latest_path = models_dir / "latest_checkpoint.pth"
        torch.save(checkpoint_data, latest_path)

        logger.info(f"检查点已保存: {checkpoint_path}")
        return checkpoint_path

    def update_global_best_models(
        self,
        experiment_dir: Path,
        metrics: Dict[str, float],
        model_analysis: Dict[str, Any],
        dataset_name: Optional[str] = None,
    ) -> Dict[str, bool]:
        """
        更新全局最优模型链接，支持按数据集分类

        参数:
            experiment_dir: 实验目录
            metrics: 评估指标
            model_analysis: 模型分析结果
            dataset_name: 数据集名称（从配置中获取）

        返回:
            各指标是否更新的状态字典
        """
        updated_metrics = {}

        # 确定数据集名称
        if dataset_name is None:
            dataset_name = self._extract_dataset_name_from_experiment(experiment_dir)

        # 创建数据集特定的目录
        dataset_best_dir = self.best_models_dir / dataset_name
        dataset_best_dir.mkdir(parents=True, exist_ok=True)

        # 加载数据集特定的最优模型记录
        dataset_records = self._load_dataset_best_models_record(dataset_name)

        # 获取当前实验的最终模型路径
        final_model_path = experiment_dir / "models" / "final_model.pth"
        if not final_model_path.exists():
            logger.warning(f"最终模型文件不存在: {final_model_path}")
            return updated_metrics

        # 检查各个指标是否需要更新
        metrics_to_check = {
            "psnr": ("higher_better", "PSNR最优模型"),
            "ssim": ("higher_better", "SSIM最优模型"),
            "lpips": ("lower_better", "LPIPS最优模型"),
            "mae": ("lower_better", "MAE最优模型"),
        }

        # 添加模型复杂度指标
        if "parameters" in model_analysis:
            total_params = model_analysis["parameters"].get("total_parameters", float("inf"))
            metrics["parameters"] = total_params
            metrics_to_check["parameters"] = ("lower_better", "参数量最优模型")

        if "flops" in model_analysis:
            total_flops = model_analysis["flops"].get("total_flops", float("inf"))
            metrics["flops"] = total_flops
            metrics_to_check["flops"] = ("lower_better", "FLOPS最优模型")

        # 检查并更新每个指标的最优模型
        for metric_name, (direction, description) in metrics_to_check.items():
            if metric_name in metrics:
                current_value = metrics[metric_name]

                is_better = self._is_better_metric(
                    current_value, dataset_records.get(metric_name, {}).get("value"), direction
                )

                if is_better:
                    # 更新数据集特定的最优模型链接
                    link_path = dataset_best_dir / f"best_{metric_name}.pth"
                    self._create_model_link(final_model_path, link_path)

                    # 更新记录
                    dataset_records[metric_name] = {
                        "value": current_value,
                        "experiment_dir": str(experiment_dir),
                        "timestamp": self._format_experiment_timestamp(experiment_dir),
                        "description": description,
                        "dataset": dataset_name,
                    }

                    updated_metrics[metric_name] = True
                    logger.info(f"🎉 [{dataset_name}] {description}已更新: {current_value}")
                else:
                    updated_metrics[metric_name] = False

        # 保存更新后的数据集特定记录
        self._save_dataset_best_models_record(dataset_name, dataset_records)

        # 更新全局最新模型链接
        latest_global_path = self.global_models_dir / "latest_checkpoint.pth"
        self._create_model_link(final_model_path, latest_global_path)

        return updated_metrics

    def _is_better_metric(
        self, current_value: float, best_value: Optional[float], direction: str
    ) -> bool:
        """
        判断当前指标是否更优

        参数:
            current_value: 当前指标值
            best_value: 历史最优值
            direction: 优化方向 ('higher_better' 或 'lower_better')

        返回:
            是否更优
        """
        if best_value is None:
            return True

        if direction == "higher_better":
            return current_value > best_value
        else:  # lower_better
            return current_value < best_value

    def _create_model_link(self, source_path: Path, link_path: Path) -> None:
        """
        创建模型文件的软链接

        参数:
            source_path: 源文件路径
            link_path: 链接文件路径
        """
        try:
            # 删除现有链接
            if link_path.exists() or link_path.is_symlink():
                link_path.unlink()

            # 创建新的软链接
            link_path.symlink_to(source_path.resolve())
            logger.info(f"模型链接已创建: {link_path} -> {source_path}")

        except Exception as e:
            # 如果软链接失败，使用复制
            logger.warning(f"软链接创建失败，使用复制: {e}")
            try:
                shutil.copy2(source_path, link_path)
                logger.info(f"模型文件已复制: {link_path}")
            except Exception as copy_error:
                logger.error(f"模型文件复制失败: {copy_error}")

    def _load_dataset_best_models_record(self, dataset_name: str) -> Dict[str, Any]:
        """
        加载数据集特定的最优模型记录

        参数:
            dataset_name: 数据集名称

        返回:
            数据集的最优模型记录字典
        """
        dataset_record_file = self.best_models_dir / dataset_name / "best_models_info.json"

        if dataset_record_file.exists():
            try:
                import json

                with open(dataset_record_file, "r", encoding="utf-8") as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"数据集 {dataset_name} 最优模型记录加载失败: {e}")

        return {}

    def _load_best_models_record(self) -> Dict[str, Any]:
        """加载全局最优模型记录（向后兼容）"""
        if self.global_best_models_record.exists():
            try:
                import json

                with open(self.global_best_models_record, "r", encoding="utf-8") as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"全局最优模型记录加载失败: {e}")

        return {}

    def _save_dataset_best_models_record(self, dataset_name: str, records: Dict[str, Any]) -> None:
        """
        保存数据集特定的最优模型记录

        参数:
            dataset_name: 数据集名称
            records: 最优模型记录字典
        """
        dataset_record_file = self.best_models_dir / dataset_name / "best_models_info.json"
        dataset_record_file.parent.mkdir(parents=True, exist_ok=True)

        try:
            import json

            with open(dataset_record_file, "w", encoding="utf-8") as f:
                json.dump(records, f, indent=2, ensure_ascii=False)
            logger.debug(f"数据集 {dataset_name} 最优模型记录已保存: {dataset_record_file}")
        except Exception as e:
            logger.error(f"数据集 {dataset_name} 最优模型记录保存失败: {e}")

    def _save_best_models_record(self, records: Dict[str, Any]) -> None:
        """保存全局最优模型记录（向后兼容）"""
        try:
            import json

            with open(self.global_best_models_record, "w", encoding="utf-8") as f:
                json.dump(records, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"全局最优模型记录保存失败: {e}")

    def _migrate_global_records_to_dataset_specific(self) -> None:
        """
        将全局best_models_info.json迁移到数据集特定的文件

        这个方法处理现有的混合格式文件，将数据集特定的记录
        迁移到对应的数据集目录中
        """
        if not self.global_best_models_record.exists():
            return

        try:
            global_records = self._load_best_models_record()
            if not global_records:
                return

            # 查找数据集特定的记录
            datasets_found = set()
            for key, value in global_records.items():
                if isinstance(value, dict) and "dataset" in value:
                    # 这是一个数据集特定的记录组
                    datasets_found.add(key)
                elif isinstance(value, dict) and isinstance(value.get("value"), dict):
                    # 这可能是嵌套的数据集记录
                    for sub_key, sub_value in value.items():
                        if isinstance(sub_value, dict) and "dataset" in sub_value:
                            datasets_found.add(sub_value["dataset"])

            # 迁移每个数据集的记录
            for dataset_name in datasets_found:
                dataset_records = {}

                # 从全局记录中提取该数据集的记录
                if dataset_name in global_records:
                    # 直接的数据集记录
                    dataset_records = global_records[dataset_name]
                else:
                    # 查找嵌套的数据集记录
                    for key, value in global_records.items():
                        if isinstance(value, dict) and value.get("dataset") == dataset_name:
                            dataset_records[key] = value

                if dataset_records:
                    # 保存到数据集特定的文件
                    self._save_dataset_best_models_record(dataset_name, dataset_records)
                    logger.info(f"已迁移数据集 {dataset_name} 的最优模型记录")

            # 创建备份并清理全局文件中的数据集特定记录
            if datasets_found:
                backup_file = self.global_best_models_record.with_suffix(".backup.json")
                import shutil

                shutil.copy2(self.global_best_models_record, backup_file)
                logger.info(f"全局记录已备份到: {backup_file}")

                # 保留非数据集特定的记录
                cleaned_records = {}
                for key, value in global_records.items():
                    if key not in datasets_found and not (
                        isinstance(value, dict) and "dataset" in value
                    ):
                        cleaned_records[key] = value

                # 保存清理后的全局记录
                self._save_best_models_record(cleaned_records)
                logger.info("全局记录已清理，移除了数据集特定的记录")

        except Exception as e:
            logger.warning(f"记录迁移失败: {e}")
            import traceback

            logger.debug(f"迁移错误详情: {traceback.format_exc()}")

    def _format_experiment_timestamp(self, experiment_dir: Path) -> str:
        """
        格式化实验时间戳为统一格式

        Args:
            experiment_dir: 实验目录路径

        Returns:
            格式化的时间戳字符串 (YYYY-MM-DD_HH-MM-SS)
        """
        dir_path = str(experiment_dir)

        # 处理新格式：YYYY-MM-DD/HH-MM-SS -> YYYY-MM-DD_HH-MM-SS
        if "/" in experiment_dir.name and len(experiment_dir.name.split("/")) == 2:
            # 这是旧的分层目录格式
            parts = dir_path.split("/")
            if len(parts) >= 2:
                date_part = parts[-2]  # YYYY-MM-DD
                time_part = parts[-1]  # HH-MM-SS
                return f"{date_part}_{time_part}"

        # 处理新格式：YYYY-MM-DD_HH-MM-SS
        if "_" in experiment_dir.name and len(experiment_dir.name.split("_")) >= 2:
            return experiment_dir.name

        # 处理旧格式：只有时间部分 HH-MM-SS
        if len(experiment_dir.name.split("-")) == 3 and all(
            part.isdigit() for part in experiment_dir.name.split("-")
        ):
            # 尝试从父目录获取日期
            parent_name = experiment_dir.parent.name
            if len(parent_name.split("-")) == 3 and parent_name.count("-") == 2:
                return f"{parent_name}_{experiment_dir.name}"

        # 默认返回原始名称
        return experiment_dir.name

    def _parse_timestamp_for_compatibility(self, timestamp: str) -> str:
        """
        解析时间戳以保持向后兼容性

        Args:
            timestamp: 时间戳字符串

        Returns:
            标准化的时间戳字符串
        """
        # 如果已经是新格式，直接返回
        if "_" in timestamp and len(timestamp.split("_")) == 2:
            return timestamp

        # 如果是旧格式的时间部分，需要更多上下文信息
        # 这里只能返回原始值，在实际使用时需要结合目录信息
        return timestamp

    def cleanup_experiment_models(
        self, experiment_dir: Path, keep_final: bool = True, keep_latest: bool = True
    ) -> None:
        """
        清理实验目录中的模型文件

        参数:
            experiment_dir: 实验目录
            keep_final: 是否保留最终模型
            keep_latest: 是否保留最新检查点
        """
        models_dir = experiment_dir / "models"
        if not models_dir.exists():
            return

        files_to_keep = set()
        if keep_final:
            files_to_keep.add("final_model.pth")
        if keep_latest:
            files_to_keep.add("latest_checkpoint.pth")

        # 删除其他检查点文件
        for model_file in models_dir.glob("*.pth"):
            if model_file.name not in files_to_keep:
                try:
                    model_file.unlink()
                    logger.info(f"已清理模型文件: {model_file}")
                except Exception as e:
                    logger.warning(f"模型文件清理失败: {model_file}, {e}")

    def get_best_model_info(self) -> Dict[str, Any]:
        """
        获取最优模型信息

        返回:
            最优模型信息字典
        """
        return self._load_best_models_record()

    def find_model_by_experiment(self, experiment_timestamp: str) -> Optional[Path]:
        """
        根据实验时间戳查找模型文件

        参数:
            experiment_timestamp: 实验时间戳

        返回:
            模型文件路径（如果存在）
        """
        # 在时间戳目录中查找
        experiment_dir = self.base_output_dir / experiment_timestamp.replace("/", "/")
        final_model = experiment_dir / "models" / "final_model.pth"

        if final_model.exists():
            return final_model

        # 在最优模型中查找
        best_records = self._load_best_models_record()
        for metric_info in best_records.values():
            if (
                isinstance(metric_info, dict)
                and metric_info.get("timestamp") == experiment_timestamp
            ):
                model_path = Path(metric_info["experiment_dir"]) / "models" / "final_model.pth"
                if model_path.exists():
                    return model_path

        return None

    def _extract_dataset_name_from_experiment(self, experiment_dir: Path) -> str:
        """
        从实验目录中提取数据集名称

        参数:
            experiment_dir: 实验目录路径

        返回:
            数据集名称，默认为'unknown'
        """
        try:
            # 尝试从实验配置文件中读取数据集名称
            config_files = [
                experiment_dir / "config.yaml",
                experiment_dir / "hydra_config.yaml",
                experiment_dir / ".hydra" / "config.yaml",
            ]

            for config_file in config_files:
                if config_file.exists():
                    dataset_name = self._extract_dataset_from_config(config_file)
                    if dataset_name:
                        return dataset_name

            # 如果无法从配置文件获取，尝试从实验目录名称推断
            dir_name = experiment_dir.name
            if "LOLv2" in dir_name:
                if "Real" in dir_name:
                    return "LOLv2_Real"
                elif "Syn" in dir_name:
                    return "LOLv2_Synthetic"
                else:
                    return "LOLv2"
            elif "LOLv1" in dir_name or "LOL" in dir_name:
                return "LOLv1"
            elif "DICM" in dir_name:
                return "DICM"
            elif "LIME" in dir_name:
                return "LIME"

            logger.warning(f"无法确定数据集名称，使用默认值: {experiment_dir}")
            return "unknown"

        except Exception as e:
            logger.warning(f"提取数据集名称失败: {e}")
            return "unknown"

    def _extract_dataset_from_config(self, config_file: Path) -> Optional[str]:
        """
        从配置文件中提取数据集名称

        参数:
            config_file: 配置文件路径

        返回:
            数据集名称或None
        """
        try:
            import yaml

            with open(config_file, "r", encoding="utf-8") as f:
                config = yaml.safe_load(f)

            # 尝试多种可能的配置路径
            dataset_paths = [
                ["dataset", "name"],
                ["dataset", "type"],
                ["data", "name"],
                ["data", "dataset"],
                ["defaults"],  # Hydra defaults中可能包含数据集信息
            ]

            for path in dataset_paths:
                value = config
                for key in path:
                    if isinstance(value, dict) and key in value:
                        value = value[key]
                    else:
                        value = None
                        break

                if value:
                    if isinstance(value, str):
                        return value
                    elif isinstance(value, list) and path == ["defaults"]:
                        # 在Hydra defaults中查找数据集配置
                        for item in value:
                            if isinstance(item, dict) and "dataset" in item:
                                return item["dataset"]
                            elif isinstance(item, str) and item.startswith("dataset:"):
                                return item.split(":", 1)[1].strip()
                            elif isinstance(item, str) and not item.startswith("_") and not item.startswith("task") and not item.startswith("model") and not item.startswith("trainer"):
                                # 可能是数据集配置文件名
                                if "LOL" in item or "DICM" in item or "LIME" in item:
                                    return item

            return None

        except Exception as e:
            logger.debug(f"配置文件解析失败 {config_file}: {e}")
            return None
