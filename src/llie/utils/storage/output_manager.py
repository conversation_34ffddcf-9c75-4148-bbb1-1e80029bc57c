"""
输出文件管理器

统一管理实验输出的文件组织、存储策略和空间优化。
"""

from pathlib import Path
from typing import Dict, Any, Optional, List, Union
from datetime import datetime
from loguru import logger

from .model_manager import ModelManager
from .result_manager import ResultManager
from .path_manager import PathManager


class OutputManager:
    """
    输出文件管理器

    主要功能：
    1. 统一管理实验输出的目录结构
    2. 协调模型文件和结果文件的存储
    3. 提供存储空间优化策略
    4. 支持实验文件的查找和清理

    新的目录结构：
    outputs/
    ├── models/                    # 全局模型目录
    │   ├── best_models/          # 各指标最优模型软链接
    │   └── latest_checkpoint.pth # 最新模型软链接
    ├── results/                  # 全局结果目录
    │   └── experiments_summary.csv # 实验结果汇总
    ├── train/                    # 训练任务输出
    │   └── YYYY-MM-DD/
    │       └── HH-MM-SS/
    ├── evaluate/                 # 评估任务输出
    │   └── YYYY-MM-DD/
    │       └── HH-MM-SS/
    └── inference/                # 推理任务输出
        └── YYYY-MM-DD/
            └── HH-MM-SS/
    """

    def __init__(self, base_output_dir: Union[str, Path] = "./outputs", task_type: Optional[str] = None):
        """
        初始化输出管理器

        Args:
            base_output_dir: 基础输出目录
            task_type: 任务类型 (train/evaluate/inference)，用于自动检测或指定
        """
        self.base_output_dir = Path(base_output_dir)
        self.task_type = task_type

        # 初始化路径管理器
        self.path_manager = PathManager(self.base_output_dir)

        # 如果没有指定任务类型，尝试从路径检测
        if not self.task_type:
            self.task_type = self.path_manager.detect_task_type_from_path(self.base_output_dir)

        # 初始化子管理器
        # ModelManager和ResultManager始终使用全局输出目录，避免在任务目录下创建models/results
        # 强制使用./outputs作为全局目录，不管OutputManager用什么目录初始化
        global_output_dir = Path("./outputs").resolve()
        self.model_manager = ModelManager(str(global_output_dir))
        self.result_manager = ResultManager(str(global_output_dir))

        logger.info(f"输出管理器初始化完成: {self.base_output_dir}, 任务类型: {self.task_type}")

    def create_experiment_directory(self, task_type: Optional[str] = None, experiment_name: Optional[str] = None) -> Path:
        """
        创建实验目录（使用新的路径管理器）

        Args:
            task_type: 任务类型，如果未指定则使用初始化时的任务类型
            experiment_name: 实验名称（可选）

        Returns:
            实验目录路径
        """
        # 确定任务类型
        effective_task_type = task_type or self.task_type
        if not effective_task_type:
            raise ValueError("必须指定任务类型或在初始化时提供任务类型")

        # 使用路径管理器创建目录
        experiment_dir = self.path_manager.create_task_directory(effective_task_type, experiment_name)

        logger.info(f"实验目录已创建: {experiment_dir}")
        return experiment_dir

    def create_model_symlink(self, source_model_path: Union[str, Path],
                           target_dir: Union[str, Path],
                           link_name: str = "used_model.pth") -> Path:
        """
        创建模型软链接（委托给路径管理器）

        Args:
            source_model_path: 源模型文件路径
            target_dir: 目标目录
            link_name: 链接名称

        Returns:
            创建的软链接路径
        """
        return self.path_manager.create_model_symlink(source_model_path, target_dir, link_name)

    def normalize_legacy_path(self, old_path: Union[str, Path], task_type: str) -> Path:
        """
        将旧格式路径转换为新的目录结构

        Args:
            old_path: 旧路径
            task_type: 任务类型

        Returns:
            新的路径
        """
        return self.path_manager.normalize_path_to_new_structure(old_path, task_type)

    def save_experiment_checkpoint(
        self,
        experiment_dir: Path,
        checkpoint_data: Dict[str, Any],
        epoch: int,
        is_final: bool = False,
    ) -> Path:
        """
        保存实验检查点

        Args:
            experiment_dir: 实验目录
            checkpoint_data: 检查点数据
            epoch: 训练轮次
            is_final: 是否为最终模型

        Returns:
            保存的检查点路径
        """
        return self.model_manager.save_checkpoint(checkpoint_data, experiment_dir, epoch, is_final)

    def save_experiment_results(
        self,
        experiment_dir: Path,
        metrics: Dict[str, float],
        model_analysis: Dict[str, Any],
        detailed_results: List[Dict[str, Any]],
        config: Optional[Dict[str, Any]] = None,
        image_files: Optional[List[Path]] = None,
        low_light_images: Optional[List[Path]] = None,
        max_image_samples: Optional[int] = 50,
        update_best_models: bool = True,
    ) -> Dict[str, bool]:
        """
        保存实验结果

        Args:
            experiment_dir: 实验目录
            metrics: 评估指标
            model_analysis: 模型分析结果
            detailed_results: 详细结果
            config: 实验配置（可选）
            image_files: 增强图片文件列表（可选）
            low_light_images: 原始低光图片文件列表（可选）
            max_image_samples: 最大图片样例数
            update_best_models: 是否更新最佳模型（默认True，评估任务应设为False）

        Returns:
            各指标是否更新的状态字典
        """
        # 保存评估结果
        self.result_manager.save_evaluation_results(
            experiment_dir, metrics, model_analysis, detailed_results, config
        )

        # 管理评估图片 - 确保保存到evaluation子目录
        if image_files:
            evaluation_dir = experiment_dir / "evaluation"
            self.result_manager.manage_evaluation_images(
                evaluation_dir=evaluation_dir,
                image_files=image_files,
                max_samples=max_image_samples,
                selection_strategy="random",
                create_comparison=True,
                low_light_images=low_light_images,
                detailed_results=detailed_results,
            )

        # 从配置中提取数据集名称
        dataset_name = None
        if config and "dataset" in config:
            dataset_config = config["dataset"]
            if isinstance(dataset_config, dict):
                dataset_name = dataset_config.get("name") or dataset_config.get("type")
            elif isinstance(dataset_config, str):
                dataset_name = dataset_config

        # 更新全局最优模型（仅在训练任务中）
        updated_metrics = {}
        if update_best_models:
            updated_metrics = self.model_manager.update_global_best_models(
                experiment_dir, metrics, model_analysis, dataset_name
            )

        return updated_metrics

    def cleanup_experiment(
        self,
        experiment_dir: Path,
        keep_final_model: bool = True,
        keep_latest_checkpoint: bool = False,
        keep_evaluation_images: bool = True,
    ) -> None:
        """
        清理实验文件

        Args:
            experiment_dir: 实验目录
            keep_final_model: 是否保留最终模型
            keep_latest_checkpoint: 是否保留最新检查点
            keep_evaluation_images: 是否保留评估图片
        """
        logger.info(f"开始清理实验目录: {experiment_dir}")

        # 清理模型文件
        self.model_manager.cleanup_experiment_models(
            experiment_dir, keep_final_model, keep_latest_checkpoint
        )

        # 清理评估图片（如果不保留）
        if not keep_evaluation_images:
            images_dir = experiment_dir / "evaluation" / "images"
            if images_dir.exists():
                import shutil

                try:
                    shutil.rmtree(images_dir)
                    logger.info(f"已清理评估图片: {images_dir}")
                except Exception as e:
                    logger.warning(f"评估图片清理失败: {e}")

        logger.info("实验目录清理完成")

    def get_experiment_summary(self) -> Dict[str, Any]:
        """
        获取实验汇总信息

        Returns:
            实验汇总信息字典
        """
        summary = {
            "total_experiments": 0,
            "best_models": {},
            "recent_experiments": [],
            "storage_info": {},
        }

        try:
            # 统计实验数量
            experiment_dirs = [
                d
                for d in self.base_output_dir.iterdir()
                if d.is_dir() and not d.name.startswith(".") and d.name not in ["models", "results"]
            ]
            summary["total_experiments"] = len(experiment_dirs)

            # 获取最优模型信息
            summary["best_models"] = self.model_manager.get_best_model_info()

            # 获取最近的实验
            recent_dirs = sorted(experiment_dirs, key=lambda x: x.name, reverse=True)[:5]
            summary["recent_experiments"] = [d.name for d in recent_dirs]

            # 计算存储信息
            summary["storage_info"] = self._calculate_storage_info()

        except Exception as e:
            logger.error(f"获取实验汇总失败: {e}")

        return summary

    def _calculate_storage_info(self) -> Dict[str, Any]:
        """计算存储信息"""
        storage_info = {
            "total_size_mb": 0,
            "models_size_mb": 0,
            "results_size_mb": 0,
            "logs_size_mb": 0,
        }

        try:

            def get_dir_size(path: Path) -> float:
                """获取目录大小（MB）"""
                total_size = 0
                if path.exists():
                    for file_path in path.rglob("*"):
                        if file_path.is_file():
                            total_size += file_path.stat().st_size
                return total_size / (1024 * 1024)  # 转换为MB

            # 计算各部分大小
            storage_info["models_size_mb"] = get_dir_size(self.base_output_dir / "models")
            storage_info["results_size_mb"] = get_dir_size(self.base_output_dir / "results")

            # 计算实验目录大小
            for exp_dir in self.base_output_dir.iterdir():
                if (
                    exp_dir.is_dir()
                    and not exp_dir.name.startswith(".")
                    and exp_dir.name not in ["models", "results"]
                ):
                    storage_info["logs_size_mb"] += get_dir_size(exp_dir / "logs")

            storage_info["total_size_mb"] = get_dir_size(self.base_output_dir)

        except Exception as e:
            logger.warning(f"存储信息计算失败: {e}")

        return storage_info

    def find_experiment_by_pattern(self, pattern: str) -> List[Path]:
        """
        根据模式查找实验目录

        Args:
            pattern: 搜索模式（支持通配符）

        Returns:
            匹配的实验目录列表
        """
        try:
            matching_dirs = list(self.base_output_dir.glob(pattern))
            # 过滤掉特殊目录
            experiment_dirs = [
                d for d in matching_dirs if d.is_dir() and d.name not in ["models", "results"]
            ]
            return sorted(experiment_dirs, key=lambda x: x.name, reverse=True)
        except Exception as e:
            logger.error(f"实验目录搜索失败: {e}")
            return []

    def archive_old_experiments(
        self, days_threshold: int = 30, archive_dir: Optional[str] = None
    ) -> None:
        """
        归档旧实验

        Args:
            days_threshold: 天数阈值，超过此天数的实验将被归档
            archive_dir: 归档目录（可选，默认为outputs/archive）
        """
        if archive_dir is None:
            archive_dir = self.base_output_dir / "archive"
        else:
            archive_dir = Path(archive_dir)

        archive_dir.mkdir(parents=True, exist_ok=True)

        from datetime import datetime, timedelta

        threshold_date = datetime.now() - timedelta(days=days_threshold)

        archived_count = 0
        for exp_dir in self.base_output_dir.iterdir():
            if (
                exp_dir.is_dir()
                and not exp_dir.name.startswith(".")
                and exp_dir.name not in ["models", "results", "archive"]
            ):
                try:
                    # 从目录名解析日期
                    date_str = exp_dir.name.split("_")[0]
                    exp_date = datetime.strptime(date_str, "%Y-%m-%d")

                    if exp_date < threshold_date:
                        # 移动到归档目录
                        archive_path = archive_dir / exp_dir.name
                        exp_dir.rename(archive_path)
                        archived_count += 1
                        logger.info(f"实验已归档: {exp_dir.name}")

                except Exception as e:
                    logger.warning(f"实验归档失败: {exp_dir.name}, {e}")

        logger.info(f"归档完成，共归档 {archived_count} 个实验")
