"""
结果存储管理器

负责评估结果的存储、图片样例的管理和数据文件的组织。
"""

import json
import shutil
from pathlib import Path
from typing import Dict, Any, Optional, List
import pandas as pd
from loguru import logger
from ..image_comparison import create_evaluation_comparison


class ResultManager:
    """
    结果存储管理器

    主要功能：
    1. 管理评估结果的存储和组织
    2. 控制图片样例的保存数量和质量
    3. 生成结果摘要和统计信息
    4. 提供结果查询和对比功能

    存储策略：
    - 评估指标以CSV和JSON格式保存
    - 图片样例按配置数量保存，支持质量筛选
    - 自动生成结果摘要和可视化图表
    """

    def __init__(self, base_output_dir: str = "./outputs"):
        """
        初始化结果管理器

        Args:
            base_output_dir: 基础输出目录
        """
        self.base_output_dir = Path(base_output_dir)

        # 创建全局结果目录
        self.global_results_dir = self.base_output_dir / "results"
        self.global_results_dir.mkdir(parents=True, exist_ok=True)

        # 结果汇总文件
        self.results_summary_file = self.global_results_dir / "experiments_summary.csv"

        logger.info(f"结果管理器初始化完成: {self.global_results_dir}")

    def save_evaluation_results(
        self,
        experiment_dir: Path,
        metrics: Dict[str, float],
        model_analysis: Dict[str, Any],
        detailed_results: List[Dict[str, Any]],
        config: Optional[Dict[str, Any]] = None,
    ) -> None:
        """
        保存评估结果

        Args:
            experiment_dir: 实验目录
            metrics: 平均评估指标
            model_analysis: 模型分析结果
            detailed_results: 详细的每张图片结果
            config: 实验配置（可选）
        """
        evaluation_dir = experiment_dir / "evaluation"
        evaluation_dir.mkdir(parents=True, exist_ok=True)

        # 1. 保存详细指标到CSV
        self._save_detailed_metrics(evaluation_dir, detailed_results)

        # 2. 保存摘要指标
        self._save_summary_metrics(evaluation_dir, metrics, model_analysis)

        # 3. 保存模型分析结果
        self._save_model_analysis(evaluation_dir, model_analysis)

        # 4. 更新全局结果汇总
        self._update_global_summary(experiment_dir, metrics, model_analysis, config)

        logger.info(f"评估结果已保存到: {evaluation_dir}")

    def _save_detailed_metrics(
        self, evaluation_dir: Path, detailed_results: List[Dict[str, Any]]
    ) -> None:
        """保存详细指标到CSV文件"""
        try:
            df = pd.DataFrame(detailed_results)
            detailed_path = evaluation_dir / "detailed_metrics.csv"
            df.to_csv(detailed_path, index=False, encoding="utf-8")
            logger.info(f"详细指标已保存: {detailed_path}")
        except Exception as e:
            logger.error(f"详细指标保存失败: {e}")

    def _save_summary_metrics(
        self, evaluation_dir: Path, metrics: Dict[str, float], model_analysis: Dict[str, Any]
    ) -> None:
        """保存摘要指标"""
        try:
            # 合并评估指标和模型分析
            summary_data = {**metrics}

            # 添加模型复杂度信息
            if "parameters" in model_analysis:
                params = model_analysis["parameters"]
                summary_data["total_parameters"] = params.get("total_parameters", 0)
                summary_data["trainable_parameters"] = params.get("trainable_parameters", 0)

            if "flops" in model_analysis:
                summary_data["total_flops"] = model_analysis["flops"].get("total_flops", 0)

            if "timing" in model_analysis:
                timing = model_analysis["timing"]
                summary_data["mean_inference_time"] = timing.get("mean_time", 0)
                summary_data["std_inference_time"] = timing.get("std_time", 0)

            # 保存为CSV
            summary_df = pd.DataFrame([summary_data])
            summary_path = evaluation_dir / "summary_metrics.csv"
            summary_df.to_csv(summary_path, index=False, encoding="utf-8")

            logger.info(f"摘要指标已保存: {summary_path}")

        except Exception as e:
            logger.error(f"摘要指标保存失败: {e}")

    def _save_model_analysis(self, evaluation_dir: Path, model_analysis: Dict[str, Any]) -> None:
        """保存模型分析结果"""
        try:
            # 确保数据可序列化
            serializable_analysis = self._make_serializable(model_analysis)

            analysis_path = evaluation_dir / "model_analysis.json"
            with open(analysis_path, "w", encoding="utf-8") as f:
                json.dump(serializable_analysis, f, indent=2, ensure_ascii=False, default=str)

            logger.info(f"模型分析已保存: {analysis_path}")

        except Exception as e:
            logger.error(f"模型分析保存失败: {e}")

    def _update_global_summary(
        self,
        experiment_dir: Path,
        metrics: Dict[str, float],
        model_analysis: Dict[str, Any],
        config: Optional[Dict[str, Any]] = None,
    ) -> None:
        """更新全局结果汇总"""
        try:
            # 准备汇总数据
            summary_row = {
                "experiment_timestamp": self._format_experiment_timestamp(experiment_dir),
                "experiment_dir": str(experiment_dir),
                **metrics,
            }

            # 添加模型信息
            if config and "model" in config:
                summary_row["model_type"] = config["model"].get("type", "Unknown")

            if "parameters" in model_analysis:
                summary_row["total_parameters"] = model_analysis["parameters"].get(
                    "total_parameters", 0
                )

            if "flops" in model_analysis:
                summary_row["total_flops"] = model_analysis["flops"].get("total_flops", 0)

            if "timing" in model_analysis:
                summary_row["mean_inference_time"] = model_analysis["timing"].get("mean_time", 0)

            # 读取现有汇总或创建新的
            if self.results_summary_file.exists():
                summary_df = pd.read_csv(self.results_summary_file)
                # 检查是否已存在该实验记录
                if experiment_dir.name not in summary_df["experiment_timestamp"].values:
                    summary_df = pd.concat(
                        [summary_df, pd.DataFrame([summary_row])], ignore_index=True
                    )
                else:
                    # 更新现有记录
                    mask = summary_df["experiment_timestamp"] == experiment_dir.name
                    for key, value in summary_row.items():
                        summary_df.loc[mask, key] = value
            else:
                summary_df = pd.DataFrame([summary_row])

            # 按时间戳排序
            summary_df = summary_df.sort_values("experiment_timestamp", ascending=False)

            # 保存汇总
            summary_df.to_csv(self.results_summary_file, index=False, encoding="utf-8")
            logger.info(f"全局结果汇总已更新: {self.results_summary_file}")

        except Exception as e:
            logger.error(f"全局结果汇总更新失败: {e}")

    def manage_evaluation_images(
        self,
        evaluation_dir: Path,
        image_files: List[Path],
        max_samples: Optional[int] = None,
        selection_strategy: str = "random",
        create_comparison: bool = True,
        low_light_images: Optional[List[Path]] = None,
        detailed_results: Optional[List[Dict[str, Any]]] = None,
    ) -> None:
        """
        管理评估图片，支持对比图片生成和智能采样

        Args:
            evaluation_dir: 评估目录
            image_files: 增强图片文件列表
            max_samples: 最大保存样例数（None表示保存全部）
            selection_strategy: 选择策略 ('random', 'best', 'worst', 'diverse')
            create_comparison: 是否创建对比图片
            low_light_images: 对应的原始低光图片列表
            detailed_results: 详细结果列表（包含指标信息）
        """
        if not image_files:
            return

        images_dir = evaluation_dir / "images"
        images_dir.mkdir(parents=True, exist_ok=True)

        # 如果不限制数量，保存所有图片
        if max_samples is None or len(image_files) <= max_samples:
            selected_files = image_files
            selected_indices = list(range(len(image_files)))
        else:
            # 根据策略选择图片
            selected_files, selected_indices = self._select_images_with_indices(
                image_files, max_samples, selection_strategy, detailed_results
            )

        # 复制选中的图片
        for img_file in selected_files:
            try:
                dest_path = images_dir / img_file.name
                shutil.copy2(img_file, dest_path)
            except Exception as e:
                logger.warning(f"图片复制失败: {img_file}, {e}")

        logger.info(f"已保存 {len(selected_files)} 张评估图片到: {images_dir}")

        # 创建对比图片（如果启用且有原始图片）
        if create_comparison and low_light_images and len(low_light_images) == len(image_files):
            self._create_comparison_images(
                evaluation_dir, selected_files, selected_indices, low_light_images, detailed_results
            )

        # 如果有图片被过滤，记录信息
        if len(selected_files) < len(image_files):
            info_file = images_dir / "selection_info.json"
            selection_info = {
                "total_images": len(image_files),
                "selected_images": len(selected_files),
                "selection_strategy": selection_strategy,
                "selected_files": [f.name for f in selected_files],
            }
            with open(info_file, "w", encoding="utf-8") as f:
                json.dump(selection_info, f, indent=2, ensure_ascii=False)

    def _select_images_with_indices(
        self,
        image_files: List[Path],
        max_samples: int,
        strategy: str,
        detailed_results: Optional[List[Dict[str, Any]]] = None,
    ) -> tuple[List[Path], List[int]]:
        """
        选择图片并返回对应的索引

        Args:
            image_files: 图片文件列表
            max_samples: 最大样本数
            strategy: 选择策略
            detailed_results: 详细结果（用于基于质量的选择）

        Returns:
            (选中的图片列表, 对应的索引列表)
        """
        import random
        import numpy as np

        if strategy == "random":
            indices = random.sample(range(len(image_files)), max_samples)
        elif strategy == "uniform":
            indices = np.linspace(0, len(image_files) - 1, max_samples, dtype=int).tolist()
        elif strategy == "best" and detailed_results:
            # 基于PSNR选择最好的图片
            psnr_values = [result.get("psnr", 0) for result in detailed_results]
            indices = sorted(range(len(psnr_values)), key=lambda i: psnr_values[i], reverse=True)[
                :max_samples
            ]
        elif strategy == "worst" and detailed_results:
            # 基于PSNR选择最差的图片
            psnr_values = [result.get("psnr", 0) for result in detailed_results]
            indices = sorted(range(len(psnr_values)), key=lambda i: psnr_values[i])[:max_samples]
        else:
            # 默认使用均匀采样
            indices = np.linspace(0, len(image_files) - 1, max_samples, dtype=int).tolist()

        selected_files = [image_files[i] for i in indices]
        return selected_files, indices

    def _create_comparison_images(
        self,
        evaluation_dir: Path,
        enhanced_images: List[Path],
        selected_indices: List[int],
        low_light_images: List[Path],
        detailed_results: Optional[List[Dict[str, Any]]] = None,
    ) -> None:
        """
        创建对比图片

        Args:
            evaluation_dir: 评估目录
            enhanced_images: 选中的增强图片
            selected_indices: 选中图片的原始索引
            low_light_images: 原始低光图片
            detailed_results: 详细结果（包含指标）
        """
        try:
            # 获取对应的原始图片
            selected_low_images = [low_light_images[i] for i in selected_indices]

            # 准备指标信息
            selected_metrics = None
            if detailed_results:
                selected_metrics = [detailed_results[i] for i in selected_indices]

            # 创建对比图片
            comparison_path = evaluation_dir / "comparison_grid.jpg"
            create_evaluation_comparison(
                low_images=selected_low_images,
                enhanced_images=enhanced_images,
                output_path=comparison_path,
                metrics_list=selected_metrics,
                max_samples=6,  # 最多显示6张图片
                layout="grid",
            )

            logger.info(f"对比图片已创建: {comparison_path}")

        except Exception as e:
            logger.warning(f"对比图片创建失败: {e}")

    def _select_images(
        self, image_files: List[Path], max_samples: int, strategy: str
    ) -> List[Path]:
        """
        根据策略选择图片

        Args:
            image_files: 图片文件列表
            max_samples: 最大样例数
            strategy: 选择策略

        Returns:
            选中的图片文件列表
        """
        if strategy == "random":
            import random

            return random.sample(image_files, max_samples)
        elif strategy == "first":
            return image_files[:max_samples]
        elif strategy == "last":
            return image_files[-max_samples:]
        elif strategy == "evenly_spaced":
            # 均匀间隔选择
            step = len(image_files) // max_samples
            return [image_files[i * step] for i in range(max_samples)]
        else:
            # 默认使用随机策略
            import random

            return random.sample(image_files, max_samples)

    def _make_serializable(self, obj) -> Any:
        """确保对象可以JSON序列化"""
        if hasattr(obj, "to_container"):  # OmegaConf对象
            return obj.to_container(resolve=True)
        elif isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return [self._make_serializable(item) for item in obj]
        elif hasattr(obj, "item"):  # numpy数值
            return obj.item()
        elif hasattr(obj, "tolist"):  # numpy数组
            return obj.tolist()
        else:
            return obj

    def get_experiment_results(self, experiment_timestamp: str) -> Optional[Dict[str, Any]]:
        """
        获取指定实验的结果

        Args:
            experiment_timestamp: 实验时间戳

        Returns:
            实验结果字典（如果存在）
        """
        try:
            if self.results_summary_file.exists():
                summary_df = pd.read_csv(self.results_summary_file)
                mask = summary_df["experiment_timestamp"] == experiment_timestamp
                if mask.any():
                    return summary_df[mask].iloc[0].to_dict()
        except Exception as e:
            logger.error(f"获取实验结果失败: {e}")

        return None

    def get_best_experiments(self, metric_name: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """
        获取指定指标的最优实验

        Args:
            metric_name: 指标名称
            top_k: 返回前k个结果

        Returns:
            最优实验列表
        """
        try:
            if self.results_summary_file.exists():
                summary_df = pd.read_csv(self.results_summary_file)
                if metric_name in summary_df.columns:
                    # 根据指标类型确定排序方向
                    ascending = metric_name.lower() in [
                        "mae",
                        "mse",
                        "lpips",
                        "total_parameters",
                        "total_flops",
                    ]
                    top_experiments = (
                        summary_df.nlargest(top_k, metric_name)
                        if not ascending
                        else summary_df.nsmallest(top_k, metric_name)
                    )
                    return top_experiments.to_dict("records")
        except Exception as e:
            logger.error(f"获取最优实验失败: {e}")

        return []

    def _format_experiment_timestamp(self, experiment_dir: Path) -> str:
        """
        格式化实验时间戳为统一格式

        Args:
            experiment_dir: 实验目录路径

        Returns:
            格式化的时间戳字符串 (YYYY-MM-DD_HH-MM-SS)
        """
        dir_path = str(experiment_dir)

        # 处理新格式：YYYY-MM-DD/HH-MM-SS -> YYYY-MM-DD_HH-MM-SS
        if "/" in experiment_dir.name and len(experiment_dir.name.split("/")) == 2:
            # 这是旧的分层目录格式
            parts = dir_path.split("/")
            if len(parts) >= 2:
                date_part = parts[-2]  # YYYY-MM-DD
                time_part = parts[-1]  # HH-MM-SS
                return f"{date_part}_{time_part}"

        # 处理新格式：YYYY-MM-DD_HH-MM-SS
        if "_" in experiment_dir.name and len(experiment_dir.name.split("_")) >= 2:
            return experiment_dir.name

        # 处理旧格式：只有时间部分 HH-MM-SS
        if len(experiment_dir.name.split("-")) == 3 and all(
            part.isdigit() for part in experiment_dir.name.split("-")
        ):
            # 尝试从父目录获取日期
            parent_name = experiment_dir.parent.name
            if len(parent_name.split("-")) == 3 and parent_name.count("-") == 2:
                return f"{parent_name}_{experiment_dir.name}"

        # 默认返回原始名称
        return experiment_dir.name
