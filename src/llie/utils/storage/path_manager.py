"""
统一路径管理器

提供统一的路径管理功能，支持按任务类型分离的目录结构。
"""

from pathlib import Path
from typing import Optional, Union
from datetime import datetime
from loguru import logger


class PathManager:
    """
    统一路径管理器
    
    主要功能：
    1. 统一时间戳格式为 YYYY-MM-DD/HH-MM-SS
    2. 按任务类型分离输出目录
    3. 提供路径检测和验证功能
    4. 支持Hydra集成
    
    目录结构：
    outputs/
    ├── train/
    │   └── YYYY-MM-DD/
    │       └── HH-MM-SS/
    ├── evaluate/
    │   └── YYYY-MM-DD/
    │       └── HH-MM-SS/
    └── inference/
        └── YYYY-MM-DD/
            └── HH-MM-SS/
    """
    
    SUPPORTED_TASKS = ["train", "evaluate", "inference"]
    TIMESTAMP_FORMAT = "%Y-%m-%d/%H-%M-%S"
    
    def __init__(self, base_output_dir: Union[str, Path] = "./outputs"):
        """
        初始化路径管理器
        
        Args:
            base_output_dir: 基础输出目录
        """
        self.base_output_dir = Path(base_output_dir)
        self.base_output_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"路径管理器初始化完成: {self.base_output_dir}")
    
    def create_task_directory(self, task_type: str, experiment_name: Optional[str] = None) -> Path:
        """
        创建任务目录
        
        Args:
            task_type: 任务类型 (train/evaluate/inference)
            experiment_name: 实验名称（可选）
            
        Returns:
            创建的任务目录路径
            
        Raises:
            ValueError: 如果任务类型不支持
        """
        if task_type not in self.SUPPORTED_TASKS:
            raise ValueError(f"不支持的任务类型: {task_type}. 支持的类型: {self.SUPPORTED_TASKS}")
        
        # 生成时间戳
        timestamp = datetime.now().strftime(self.TIMESTAMP_FORMAT)
        
        # 构建目录路径
        if experiment_name:
            dir_name = f"{timestamp.replace('/', '_')}_{experiment_name}"
            task_dir = self.base_output_dir / task_type / dir_name
        else:
            task_dir = self.base_output_dir / task_type / timestamp
        
        # 创建目录
        task_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建标准子目录
        self._create_standard_subdirs(task_dir, task_type)
        
        logger.info(f"任务目录已创建: {task_dir}")
        return task_dir
    
    def _create_standard_subdirs(self, task_dir: Path, task_type: str) -> None:
        """
        创建标准子目录结构
        
        Args:
            task_dir: 任务目录
            task_type: 任务类型
        """
        if task_type == "train":
            subdirs = ["config", "logs", "models", "evaluation"]
        elif task_type == "evaluate":
            subdirs = ["config", "logs", "models", "evaluation"]
        elif task_type == "inference":
            subdirs = ["config", "logs", "models"]  # 移除results，推理任务直接在根目录保存文件
        else:
            subdirs = ["config", "logs"]
        
        for subdir in subdirs:
            (task_dir / subdir).mkdir(exist_ok=True)
    
    def detect_task_type_from_path(self, path: Union[str, Path]) -> Optional[str]:
        """
        从路径检测任务类型
        
        Args:
            path: 路径
            
        Returns:
            检测到的任务类型，如果无法检测则返回None
        """
        path = Path(path)
        
        # 检查路径中是否包含任务类型
        for part in path.parts:
            if part in self.SUPPORTED_TASKS:
                return part
        
        # 检查是否是Hydra生成的路径，尝试从配置推断
        if self._is_hydra_output_dir(path):
            return self._infer_task_from_hydra_dir(path)
        
        return None
    
    def _is_hydra_output_dir(self, path: Path) -> bool:
        """
        检测是否是Hydra生成的输出目录
        
        Args:
            path: 路径
            
        Returns:
            是否是Hydra输出目录
        """
        # Hydra通常生成形如 outputs/YYYY-MM-DD/HH-MM-SS 的目录
        if len(path.parts) >= 3:
            date_part = path.parts[-2]
            time_part = path.parts[-1]
            
            # 检查日期格式 YYYY-MM-DD
            if (len(date_part) == 10 and date_part.count("-") == 2):
                try:
                    year, month, day = map(int, date_part.split("-"))
                    if 1900 <= year <= 2100 and 1 <= month <= 12 and 1 <= day <= 31:
                        # 检查时间格式 HH-MM-SS
                        if (len(time_part) == 8 and time_part.count("-") == 2):
                            hour, minute, second = map(int, time_part.split("-"))
                            return 0 <= hour <= 23 and 0 <= minute <= 59 and 0 <= second <= 59
                except ValueError:
                    pass
        
        return False
    
    def _infer_task_from_hydra_dir(self, path: Path) -> Optional[str]:
        """
        从Hydra目录推断任务类型
        
        Args:
            path: Hydra输出目录路径
            
        Returns:
            推断的任务类型
        """
        # 检查目录中的文件来推断任务类型
        if (path / "models" / "final_model.pth").exists():
            return "train"
        elif (path / "evaluation" / "detailed_metrics.csv").exists():
            return "evaluate"
        elif (path / "results").exists():
            return "inference"
        
        return None
    
    def normalize_path_to_new_structure(self, old_path: Union[str, Path], task_type: str) -> Path:
        """
        将旧路径格式转换为新的目录结构
        
        Args:
            old_path: 旧路径
            task_type: 任务类型
            
        Returns:
            新的路径
        """
        old_path = Path(old_path)
        
        # 提取时间戳信息
        timestamp = self._extract_timestamp_from_path(old_path)
        if not timestamp:
            # 如果无法提取时间戳，使用当前时间
            timestamp = datetime.now().strftime(self.TIMESTAMP_FORMAT)
        
        # 构建新路径
        new_path = self.base_output_dir / task_type / timestamp
        return new_path
    
    def _extract_timestamp_from_path(self, path: Path) -> Optional[str]:
        """
        从路径中提取时间戳
        
        Args:
            path: 路径
            
        Returns:
            提取的时间戳字符串，格式为 YYYY-MM-DD/HH-MM-SS
        """
        path_str = str(path)
        
        # 处理 YYYY-MM-DD_HH-MM-SS 格式
        if "_" in path.name and len(path.name) == 19:
            date_part, time_part = path.name.split("_")
            if (len(date_part) == 10 and len(time_part) == 8 and 
                date_part.count("-") == 2 and time_part.count("-") == 2):
                return f"{date_part}/{time_part}"
        
        # 处理 YYYY-MM-DD/HH-MM-SS 格式
        if self._is_hydra_output_dir(path):
            date_part = path.parts[-2]
            time_part = path.parts[-1]
            return f"{date_part}/{time_part}"
        
        return None
    
    def get_global_output_dir(self) -> Path:
        """
        获取全局输出目录（用于全局模型和结果管理）
        
        Returns:
            全局输出目录路径
        """
        return self.base_output_dir
    
    def create_model_symlink(self, source_model_path: Union[str, Path], 
                           target_dir: Union[str, Path], 
                           link_name: str = "used_model.pth") -> Path:
        """
        创建模型软链接
        
        Args:
            source_model_path: 源模型文件路径
            target_dir: 目标目录
            link_name: 链接名称
            
        Returns:
            创建的软链接路径
        """
        source_path = Path(source_model_path)
        target_dir = Path(target_dir)
        link_path = target_dir / link_name
        
        # 确保目标目录存在
        target_dir.mkdir(parents=True, exist_ok=True)
        
        # 删除已存在的链接
        if link_path.exists() or link_path.is_symlink():
            link_path.unlink()
        
        # 创建软链接
        try:
            link_path.symlink_to(source_path.resolve())
            logger.info(f"模型软链接已创建: {link_path} -> {source_path}")
        except OSError as e:
            logger.warning(f"创建软链接失败，复制文件: {e}")
            import shutil
            shutil.copy2(source_path, link_path)
            logger.info(f"模型文件已复制: {link_path}")
        
        return link_path
