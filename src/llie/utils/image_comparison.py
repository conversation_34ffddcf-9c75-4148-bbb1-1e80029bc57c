"""
图片对比和拼接工具

主要功能：
1. 创建原图-生成图对比展示
2. 多张图片拼接成网格布局
3. 优化存储空间同时保持可视化效果
4. 支持不同的对比布局模式
"""

import numpy as np
from PIL import Image, ImageDraw, ImageFont
from pathlib import Path
from typing import List, Tuple, Optional, Union
import torch
from loguru import logger


class ImageComparisonGenerator:
    """
    图片对比生成器

    功能：
    - 创建原图-增强图对比展示
    - 支持多种布局模式（网格、并排等）
    - 自动添加标签和指标信息
    - 优化输出图片质量和大小
    """

    def __init__(
        self,
        font_size: int = 20,
        padding: int = 10,
        background_color: Tuple[int, int, int] = (255, 255, 255),
    ):
        """
        初始化图片对比生成器

        Args:
            font_size: 标签字体大小
            padding: 图片间距
            background_color: 背景颜色 (R, G, B)
        """
        self.font_size = font_size
        self.padding = padding
        self.background_color = background_color

        # 尝试加载字体
        try:
            # 尝试加载中文字体
            self.font = ImageFont.truetype(
                "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", font_size
            )
        except:
            # 使用默认字体
            self.font = ImageFont.load_default()
            logger.warning("无法加载指定字体，使用默认字体")

    def create_comparison_grid(
        self,
        image_pairs: List[Tuple[Union[str, Path, Image.Image], Union[str, Path, Image.Image]]],
        labels: Optional[List[Tuple[str, str]]] = None,
        metrics: Optional[List[dict]] = None,
        grid_cols: int = 3,
        target_size: Tuple[int, int] = (256, 256),
    ) -> Image.Image:
        """
        创建对比网格图片

        Args:
            image_pairs: 图片对列表 [(原图, 增强图), ...]
            labels: 标签对列表 [("原图", "增强图"), ...]
            metrics: 指标信息列表 [{"psnr": 28.5, "ssim": 0.85}, ...]
            grid_cols: 网格列数
            target_size: 目标图片尺寸

        Returns:
            拼接后的对比图片
        """
        if not image_pairs:
            raise ValueError("图片对列表不能为空")

        num_pairs = len(image_pairs)
        grid_rows = (num_pairs + grid_cols - 1) // grid_cols

        # 加载和调整图片尺寸
        processed_pairs = []
        for i, (low_img, enhanced_img) in enumerate(image_pairs):
            low_pil = self._load_and_resize_image(low_img, target_size)
            enhanced_pil = self._load_and_resize_image(enhanced_img, target_size)
            processed_pairs.append((low_pil, enhanced_pil))

        # 计算画布尺寸
        img_width, img_height = target_size
        label_height = self.font_size + self.padding if labels else 0

        # 每个对比单元的尺寸（原图 + 增强图 垂直排列）
        unit_width = img_width + 2 * self.padding
        unit_height = 2 * img_height + label_height + 3 * self.padding

        canvas_width = grid_cols * unit_width + self.padding
        canvas_height = grid_rows * unit_height + self.padding

        # 创建画布
        canvas = Image.new("RGB", (canvas_width, canvas_height), self.background_color)
        draw = ImageDraw.Draw(canvas)

        # 绘制图片对
        for i, (low_img, enhanced_img) in enumerate(processed_pairs):
            row = i // grid_cols
            col = i % grid_cols

            # 计算位置
            x_offset = col * unit_width + self.padding
            y_offset = row * unit_height + self.padding

            # 粘贴原图（上方）
            canvas.paste(low_img, (x_offset, y_offset))

            # 粘贴增强图（下方）
            canvas.paste(enhanced_img, (x_offset, y_offset + img_height + self.padding))

            # 添加标签
            if labels and i < len(labels):
                low_label, enhanced_label = labels[i]

                # 原图标签
                draw.text(
                    (x_offset, y_offset + img_height + self.padding // 2),
                    low_label,
                    fill=(0, 0, 0),
                    font=self.font,
                )

                # 增强图标签
                draw.text(
                    (x_offset, y_offset + 2 * img_height + self.padding + self.padding // 2),
                    enhanced_label,
                    fill=(0, 0, 0),
                    font=self.font,
                )

            # 添加指标信息
            if metrics and i < len(metrics):
                metric_text = self._format_metrics(metrics[i])
                draw.text(
                    (x_offset, y_offset + 2 * img_height + 2 * self.padding + label_height),
                    metric_text,
                    fill=(0, 100, 0),
                    font=self.font,
                )

        return canvas

    def create_side_by_side_comparison(
        self,
        image_pairs: List[Tuple[Union[str, Path, Image.Image], Union[str, Path, Image.Image]]],
        labels: Optional[List[Tuple[str, str]]] = None,
        target_size: Tuple[int, int] = (256, 256),
    ) -> Image.Image:
        """
        创建并排对比图片

        Args:
            image_pairs: 图片对列表
            labels: 标签对列表
            target_size: 目标图片尺寸

        Returns:
            并排对比图片
        """
        if not image_pairs:
            raise ValueError("图片对列表不能为空")

        # 加载和调整图片尺寸
        processed_pairs = []
        for low_img, enhanced_img in image_pairs:
            low_pil = self._load_and_resize_image(low_img, target_size)
            enhanced_pil = self._load_and_resize_image(enhanced_img, target_size)
            processed_pairs.append((low_pil, enhanced_pil))

        # 计算画布尺寸
        img_width, img_height = target_size
        label_height = self.font_size + self.padding if labels else 0

        canvas_width = len(image_pairs) * (2 * img_width + 3 * self.padding) + self.padding
        canvas_height = img_height + label_height + 2 * self.padding

        # 创建画布
        canvas = Image.new("RGB", (canvas_width, canvas_height), self.background_color)
        draw = ImageDraw.Draw(canvas)

        # 绘制图片对
        for i, (low_img, enhanced_img) in enumerate(processed_pairs):
            x_offset = i * (2 * img_width + 3 * self.padding) + self.padding
            y_offset = self.padding

            # 粘贴原图（左侧）
            canvas.paste(low_img, (x_offset, y_offset))

            # 粘贴增强图（右侧）
            canvas.paste(enhanced_img, (x_offset + img_width + self.padding, y_offset))

            # 添加标签
            if labels and i < len(labels):
                low_label, enhanced_label = labels[i]

                # 原图标签
                draw.text(
                    (x_offset, y_offset + img_height + self.padding // 2),
                    low_label,
                    fill=(0, 0, 0),
                    font=self.font,
                )

                # 增强图标签
                draw.text(
                    (
                        x_offset + img_width + self.padding,
                        y_offset + img_height + self.padding // 2,
                    ),
                    enhanced_label,
                    fill=(0, 0, 0),
                    font=self.font,
                )

        return canvas

    def _load_and_resize_image(
        self, image: Union[str, Path, Image.Image], target_size: Tuple[int, int]
    ) -> Image.Image:
        """
        加载并调整图片尺寸

        Args:
            image: 图片路径或PIL图片对象
            target_size: 目标尺寸

        Returns:
            调整后的PIL图片
        """
        if isinstance(image, (str, Path)):
            pil_image = Image.open(image).convert("RGB")
        elif isinstance(image, Image.Image):
            pil_image = image.convert("RGB")
        elif isinstance(image, torch.Tensor):
            # 处理PyTorch张量
            if image.dim() == 4:  # (B, C, H, W)
                image = image.squeeze(0)
            if image.dim() == 3:  # (C, H, W)
                image = image.permute(1, 2, 0)  # (H, W, C)

            # 转换到0-255范围
            if image.max() <= 1.0:
                image = (image * 255).clamp(0, 255)

            # 转换为numpy数组
            np_image = image.cpu().numpy().astype(np.uint8)
            pil_image = Image.fromarray(np_image)
        else:
            raise ValueError(f"不支持的图片类型: {type(image)}")

        # 调整尺寸
        return pil_image.resize(target_size, Image.Resampling.LANCZOS)

    def _format_metrics(self, metrics: dict) -> str:
        """
        格式化指标信息

        Args:
            metrics: 指标字典

        Returns:
            格式化的指标字符串
        """
        formatted_parts = []
        for key, value in metrics.items():
            if isinstance(value, float):
                formatted_parts.append(f"{key.upper()}: {value:.3f}")
            else:
                formatted_parts.append(f"{key.upper()}: {value}")

        return " | ".join(formatted_parts)


def create_evaluation_comparison(
    low_images: List[Path],
    enhanced_images: List[Path],
    output_path: Path,
    metrics_list: Optional[List[dict]] = None,
    max_samples: int = 6,
    layout: str = "grid",
) -> Path:
    """
    创建评估对比图片的便捷函数

    Args:
        low_images: 原始低光图片路径列表
        enhanced_images: 增强图片路径列表
        output_path: 输出路径
        metrics_list: 指标列表
        max_samples: 最大样本数
        layout: 布局模式 ("grid" 或 "side_by_side")

    Returns:
        生成的对比图片路径
    """
    if len(low_images) != len(enhanced_images):
        raise ValueError("原始图片和增强图片数量不匹配")

    # 限制样本数量
    num_samples = min(len(low_images), max_samples)
    selected_indices = np.linspace(0, len(low_images) - 1, num_samples, dtype=int)

    image_pairs = []
    selected_metrics = []
    labels = []

    for i, idx in enumerate(selected_indices):
        image_pairs.append((low_images[idx], enhanced_images[idx]))
        labels.append((f"原图 {i + 1}", f"增强 {i + 1}"))

        if metrics_list and idx < len(metrics_list):
            selected_metrics.append(metrics_list[idx])

    # 创建对比生成器
    generator = ImageComparisonGenerator()

    # 生成对比图片
    if layout == "grid":
        comparison_image = generator.create_comparison_grid(
            image_pairs, labels, selected_metrics if selected_metrics else None
        )
    else:  # side_by_side
        comparison_image = generator.create_side_by_side_comparison(image_pairs, labels)

    # 保存图片
    output_path.parent.mkdir(parents=True, exist_ok=True)
    comparison_image.save(output_path, quality=95)

    logger.info(f"评估对比图片已保存: {output_path}")
    return output_path
