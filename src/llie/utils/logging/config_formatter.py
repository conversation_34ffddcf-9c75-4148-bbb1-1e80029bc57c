"""
配置信息格式化器

负责将实验配置信息转换为易读的格式，支持结构化展示和对比。
"""

from typing import Dict, Any, Optional
from pathlib import Path
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.tree import Tree
from omegaconf import DictConfig, OmegaConf
from loguru import logger


class ConfigFormatter:
    """
    配置信息格式化器

    主要功能：
    1. 将配置信息转换为结构化的可视化展示
    2. 生成配置对比表格
    3. 保存配置文件到指定目录
    4. 处理OmegaConf对象的序列化问题
    """

    def __init__(self):
        """初始化配置格式化器"""
        self.console = Console()

    def display_experiment_config(self, config: DictConfig, title: str = "🔧 实验配置") -> None:
        """
        展示实验配置信息

        Args:
            config: 实验配置对象
            title: 展示标题
        """
        try:
            # 创建配置树状结构
            tree = Tree(f"[bold blue]{title}[/bold blue]")

            # 添加主要配置项
            self._add_config_branch(tree, "模型配置", config.get("model", {}))
            self._add_config_branch(tree, "数据集配置", config.get("dataset", {}))
            self._add_config_branch(tree, "训练配置", config.get("trainer", {}))
            self._add_config_branch(tree, "任务配置", config.get("task", {}))

            # 显示配置树
            self.console.print("\n")
            self.console.print(tree)
            self.console.print("\n")

        except Exception as e:
            logger.error(f"配置展示失败: {e}")

    def _add_config_branch(self, tree: Tree, branch_name: str, config_dict: Dict[str, Any]) -> None:
        """
        添加配置分支到树状结构

        Args:
            tree: 树状结构对象
            branch_name: 分支名称
            config_dict: 配置字典
        """
        if not config_dict:
            return

        branch = tree.add(f"[cyan]{branch_name}[/cyan]")

        for key, value in config_dict.items():
            if isinstance(value, dict):
                # 嵌套字典，创建子分支
                sub_branch = branch.add(f"[yellow]{key}[/yellow]")
                for sub_key, sub_value in value.items():
                    sub_branch.add(f"{sub_key}: [green]{sub_value}[/green]")
            else:
                # 简单值，直接添加
                branch.add(f"{key}: [green]{value}[/green]")

    def create_summary_table(self, config: DictConfig) -> Table:
        """
        创建配置摘要表格

        Args:
            config: 实验配置对象

        Returns:
            配置摘要表格
        """
        table = Table(title="📋 实验配置摘要", show_header=True, header_style="bold magenta")
        table.add_column("配置项", style="cyan", no_wrap=True)
        table.add_column("值", style="green")
        table.add_column("说明", style="yellow")

        # 添加关键配置信息
        model_config = config.get("model", {})
        if model_config:
            table.add_row("模型类型", str(model_config.get("type", "Unknown")), "使用的模型架构")

        dataset_config = config.get("dataset", {})
        if dataset_config:
            table.add_row("数据集", str(dataset_config.get("type", "Unknown")), "训练数据集类型")
            table.add_row(
                "批次大小", str(dataset_config.get("batch_size", "Unknown")), "每批次样本数量"
            )

        trainer_config = config.get("trainer", {})
        if trainer_config:
            table.add_row(
                "训练轮数", str(trainer_config.get("max_epochs", "Unknown")), "最大训练轮数"
            )
            table.add_row("学习率", str(trainer_config.get("lr", "Unknown")), "初始学习率")
            table.add_row(
                "优化器",
                str(trainer_config.get("optimizer", {}).get("type", "Unknown")),
                "优化算法",
            )

        return table

    def save_config_files(self, config: DictConfig, output_dir: Path) -> None:
        """
        保存配置文件到指定目录

        Args:
            config: 实验配置对象
            output_dir: 输出目录路径
        """
        try:
            config_dir = output_dir / "config"
            config_dir.mkdir(parents=True, exist_ok=True)

            # 保存完整配置
            full_config_path = config_dir / "experiment_config.yaml"
            with open(full_config_path, "w", encoding="utf-8") as f:
                OmegaConf.save(config, f)

            # 保存配置摘要（JSON格式，便于程序读取）
            summary_config = self._extract_config_summary(config)
            summary_path = config_dir / "config_summary.json"

            import json

            with open(summary_path, "w", encoding="utf-8") as f:
                json.dump(summary_config, f, indent=2, ensure_ascii=False)

            logger.info(f"配置文件已保存到: {config_dir}")

        except Exception as e:
            logger.error(f"配置文件保存失败: {e}")

    def _extract_config_summary(self, config) -> Dict[str, Any]:
        """
        提取配置摘要信息

        Args:
            config: 实验配置对象（可以是DictConfig或普通字典）

        Returns:
            配置摘要字典
        """
        summary = {}

        # 安全获取配置值的辅助函数
        def safe_get(obj, key, default="Unknown"):
            """安全获取配置值，支持DictConfig和普通字典"""
            if obj is None:
                return default
            if hasattr(obj, 'get'):  # DictConfig或字典
                return obj.get(key, default)
            elif hasattr(obj, key):  # 对象属性访问
                return getattr(obj, key, default)
            else:
                return default

        # 提取关键配置信息
        if "model" in config:
            model_config = config.get("model", {}) if hasattr(config, 'get') else config.get("model", {})
            summary["model"] = {
                "type": safe_get(model_config, "type"),
                "parameters": safe_get(model_config, "parameters", {}),
            }

        if "dataset" in config:
            dataset_config = config.get("dataset", {}) if hasattr(config, 'get') else config.get("dataset", {})
            train_config = safe_get(dataset_config, "train", {})
            summary["dataset"] = {
                "type": safe_get(dataset_config, "type"),
                "batch_size": safe_get(dataset_config, "batch_size"),
                "train_split": safe_get(train_config, "split"),
            }

        if "trainer" in config:
            trainer_config = config.get("trainer", {}) if hasattr(config, 'get') else config.get("trainer", {})
            optimizer_config = safe_get(trainer_config, "optimizer", {})
            scheduler_config = safe_get(trainer_config, "scheduler", {})
            summary["trainer"] = {
                "max_epochs": safe_get(trainer_config, "max_epochs"),
                "lr": safe_get(trainer_config, "lr"),
                "optimizer": safe_get(optimizer_config, "type"),
                "scheduler": safe_get(scheduler_config, "type"),
            }

        return summary

    @staticmethod
    def safe_serialize(obj: Any) -> Any:
        """
        安全序列化对象，处理OmegaConf等特殊类型

        Args:
            obj: 需要序列化的对象

        Returns:
            可序列化的对象
        """
        if hasattr(obj, "to_container"):  # OmegaConf对象
            return obj.to_container(resolve=True)
        elif isinstance(obj, dict):
            return {k: ConfigFormatter.safe_serialize(v) for k, v in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return [ConfigFormatter.safe_serialize(item) for item in obj]
        else:
            return obj
