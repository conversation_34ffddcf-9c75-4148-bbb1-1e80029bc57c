"""
日志系统模块

提供结构化、可读的实验日志管理功能。

主要组件：
- ExperimentLogger: 实验日志管理器，负责统一的日志输出和格式化
- ProgressTracker: 进度跟踪器，负责训练和评估进度的可视化
- ConfigFormatter: 配置格式化器，负责配置信息的结构化展示

设计原则：
1. 可读性第一：清晰的中文注释和直观的API设计
2. 模块化：每个组件职责单一，便于理解和维护
3. 易于使用：简单的接口，最少的配置
4. 避免过度工程化：不使用复杂的设计模式
"""

from .experiment_logger import ExperimentLogger
from .progress_tracker import ProgressTracker
from .config_formatter import ConfigFormatter

__all__ = ["ExperimentLogger", "ProgressTracker", "ConfigFormatter"]
