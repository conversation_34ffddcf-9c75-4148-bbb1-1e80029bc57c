"""
即插即用组件的注册表模式实现。
本模块为模型、数据集、损失函数等提供了装饰器和注册表。
"""

import functools
from typing import Dict, Type, Any, Callable, Optional, Union
import inspect
from loguru import logger


class Registry:
    """
    将字符串映射到类的注册表。

    参数:
        name: 用于标识的注册表名称

    属性:
        name: 注册表名称
        _module_dict: 将字符串名称映射到类的字典
    """

    def __init__(self, name: str) -> None:
        self.name = name
        self._module_dict: Dict[str, Any] = {}

    def __len__(self) -> int:
        return len(self._module_dict)

    def __contains__(self, key: str) -> bool:
        return key in self._module_dict

    def __repr__(self) -> str:
        format_str = (
            f"{self.__class__.__name__}(name={self.name}, items={list(self._module_dict.keys())})"
        )
        return format_str

    @property
    def module_dict(self) -> Dict[str, Any]:
        return self._module_dict

    def get(self, key: str) -> Optional[Any]:
        """获取注册表记录。

        参数:
            key: 字符串格式的类名。

        返回:
            对应的类，如果未找到则返回None。
        """
        return self._module_dict.get(key, None)

    def register(self, name: Optional[str] = None) -> Callable:
        """简化的注册装饰器，用于向后兼容。

        参数:
            name: 注册类的名称。如果为None，则使用类名。

        返回:
            装饰器函数。
        """

        def decorator(cls: Type) -> Type:
            register_name = name or cls.__name__
            if register_name in self._module_dict:
                logger.warning(f"Overriding {self.name} '{register_name}'")

            self._module_dict[register_name] = cls
            logger.debug(f"Registered {self.name}: {register_name}")
            return cls

        return decorator

    def register_module(
        self, name: Optional[str] = None, force: bool = False, module: Optional[Type] = None
    ) -> Union[Type, callable]:
        """注册一个模块。

        将向`self._module_dict`添加一条记录，其键是类名或指定名称，
        值是类本身。可以用作装饰器或普通函数。

        参数:
            name: 要注册的模块名。如果未指定，将使用类名。
            force: 是否覆盖已存在的注册模块。
            module: 要注册的模块类或函数。

        示例:
            >>> # 用作装饰器
            >>> @MODELS.register_module()
            >>> class ResNet:
            >>>     pass

            >>> # 用作带名称的函数
            >>> @MODELS.register_module(name='mynet')
            >>> class CustomNet:
            >>>     pass

        返回:
            注册的模块类。
        """

        if not isinstance(force, bool):
            raise TypeError(f"force must be a boolean, but got {type(force)}")

        # Use as a decorator: @MODELS.register_module()
        if module is None:
            return functools.partial(self.register_module, name=name, force=force)

        # Use as a function: MODELS.register_module(module=SomeClass)
        if not inspect.isclass(module) and not inspect.isfunction(module):
            raise TypeError(f"module must be a class or function, but got {type(module)}")

        module_name = name if name is not None else module.__name__

        if module_name in self._module_dict and not force:
            logger.warning(f"{module_name} is already registered in {self.name}")
        else:
            self._module_dict[module_name] = module
            logger.debug(f"Registered {module_name} in {self.name}")

        return module

    def build(self, cfg: Union[Dict, str], *args, **kwargs) -> Any:
        """从配置字典或字符串构建模块。

        参数:
            cfg: 包含模块类型和参数的配置字典或字符串。
                必须包含指定模块名称的'type'键。
            *args: 额外的位置参数。
            **kwargs: 额外的关键字参数。

        返回:
            构建的模块实例。

        示例:
            >>> model_cfg = {'type': 'ResNet', 'depth': 50}
            >>> model = MODELS.build(model_cfg)

            >>> # 或者简单地对无参数模块使用字符串
            >>> optimizer = OPTIMIZERS.build('Adam')
        """
        if isinstance(cfg, str):
            # Simple string type, no parameters
            cfg = {"type": cfg}
        elif not isinstance(cfg, dict):
            raise TypeError(f"cfg must be a dict or str, but got {type(cfg)}")

        if "type" not in cfg:
            raise KeyError('Config dict must contain "type" key')

        module_type = cfg.pop("type")
        if module_type not in self._module_dict:
            raise KeyError(
                f"{module_type} is not registered in {self.name}. "
                f"Available types: {list(self._module_dict.keys())}"
            )

        module_cls = self._module_dict[module_type]

        try:
            # Merge config parameters with additional kwargs
            merged_kwargs = {**cfg, **kwargs}
            return module_cls(*args, **merged_kwargs)
        except Exception as e:
            logger.error(f"Failed to build {module_type}: {str(e)}")
            raise


# 为不同组件类型创建全局注册表
MODELS = Registry("models")
DATASETS = Registry("datasets")
LOSSES = Registry("losses")
OPTIMIZERS = Registry("optimizers")
SCHEDULERS = Registry("schedulers")
METRICS = Registry("metrics")
TRANSFORMS = Registry("transforms")


# 便捷函数
def register_model(name: Optional[str] = None):
    """注册模型类。"""
    return MODELS.register(name)


def register_dataset(name: Optional[str] = None):
    """注册数据集类。"""
    return DATASETS.register(name)


def register_loss(name: Optional[str] = None):
    """注册损失函数类。"""
    return LOSSES.register(name)


def register_optimizer(name: Optional[str] = None):
    """注册优化器类。"""
    return OPTIMIZERS.register(name)


def register_scheduler(name: Optional[str] = None):
    """注册调度器类。"""
    return SCHEDULERS.register(name)


def register_transform(name: Optional[str] = None):
    """注册变换类。"""
    return TRANSFORMS.register(name)


def register_metric(name: Optional[str] = None):
    """注册指标类。"""
    return METRICS.register(name)


__all__ = [
    "Registry",
    "MODELS",
    "DATASETS",
    "LOSSES",
    "OPTIMIZERS",
    "SCHEDULERS",
    "METRICS",
    "TRANSFORMS",
    "register_model",
    "register_dataset",
    "register_loss",
    "register_optimizer",
    "register_scheduler",
    "register_transform",
    "register_metric",
]
