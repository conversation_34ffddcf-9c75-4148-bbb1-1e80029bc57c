"""
LLIE（低光图像增强）包

一个现代、模块化的低光图像增强研究框架。
主要特性：
- 注册表模式：支持即插即用的组件
- Hydra配置管理：强大的配置驱动系统
- 全面的实验跟踪：完整的训练和评估记录
- 高质量代码：类型提示和完整的文档

核心模块：
- models: 各种低光增强模型实现
- data: 数据集和数据加载器
- losses: 损失函数集合
- utils: 工具函数和注册表系统
"""

__version__ = "0.0.1"
__author__ = "OGAS"

from .utils.registry import MODELS, DATASETS, LOSSES, OPTIMIZERS, SCHEDULERS, METRICS

# Import all modules to trigger registrations
from . import losses, models, data, utils

__all__ = [
    "MODELS",
    "DATASETS",
    "LOSSES",
    "OPTIMIZERS",
    "SCHEDULERS",
    "METRICS",
]
