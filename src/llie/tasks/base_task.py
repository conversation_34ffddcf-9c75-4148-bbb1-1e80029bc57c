"""
LLIE任务基类，用于所有低光图像增强操作。
"""

from abc import ABC, abstractmethod
from typing import Dict, Any
from omegaconf import DictConfig
from loguru import logger


class BaseTask(ABC):
    """
    LLIE框架中所有任务的基类。

    为不同任务（训练、评估、推理等）提供通用功能和接口。
    """

    def __init__(self, config: DictConfig):
        """
        使用配置初始化任务。

        参数:
            config: Hydra配置对象。
        """
        self.config = config
        self.name = self.__class__.__name__

        logger.info(f"Initializing task: {self.name}")

    @abstractmethod
    def setup(self):
        """设置任务特定的资源和组件。"""
        pass

    @abstractmethod
    def run(self) -> Dict[str, Any]:
        """
        执行任务。

        返回:
            包含任务结果和指标的字典。
        """
        pass

    def cleanup(self):
        """任务完成后清理资源。"""
        pass

    def __enter__(self):
        """上下文管理器入口。"""
        self.setup()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器退出。"""
        self.cleanup()

        if exc_type is not None:
            logger.error(f"Task {self.name} failed with exception: {exc_val}")
        else:
            logger.success(f"Task {self.name} completed successfully")

    def validate_config(self):
        """验证任务配置。"""
        # 在子类中重写以实现特定的验证逻辑
        pass

    def get_output_dir(self):
        """获取任务结果的输出目录。"""
        from pathlib import Path

        return Path.cwd()  # Hydra manages the output directory
