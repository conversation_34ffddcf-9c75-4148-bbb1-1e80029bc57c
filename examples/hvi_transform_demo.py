#!/usr/bin/env python3
"""
HVI色彩空间转换模块使用示例

本示例演示如何在LLIE项目中使用HVI色彩空间转换模块，包括：
1. 基本的RGB↔HVI转换
2. 通过注册表系统创建组件
3. 在模型中集成HVI转换
4. 配置文件驱动的使用方式

运行要求：
- 已安装PyTorch: pip install torch torchvision
- 在LLIE项目根目录下运行: python examples/hvi_transform_demo.py
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import torch
import torch.nn as nn
from loguru import logger

def demo_basic_usage():
    """演示基本使用方法"""
    print("=== 基本使用方法演示 ===")
    
    try:
        # 导入HVI转换模块
        from LLIE.src.llie.models.components.HVITransform import HVITransform, rgb_to_hvi, hvi_to_rgb
        
        # 方法1：直接创建实例
        transform = HVITransform(density_k=0.2, learnable_k=False)
        print("✅ 创建HVITransform实例成功")
        
        # 创建测试RGB图像
        rgb_image = torch.rand(2, 3, 128, 128)
        print(f"✅ 创建测试RGB图像: {rgb_image.shape}")
        
        # RGB到HVI转换
        hvi_image = transform.rgb_to_hvi(rgb_image)
        print(f"✅ RGB到HVI转换成功: {hvi_image.shape}")
        
        # 检查HVI通道范围
        H, V, I = hvi_image[:, 0], hvi_image[:, 1], hvi_image[:, 2]
        print(f"   H通道范围: [{H.min():.3f}, {H.max():.3f}]")
        print(f"   V通道范围: [{V.min():.3f}, {V.max():.3f}]")
        print(f"   I通道范围: [{I.min():.3f}, {I.max():.3f}]")
        
        # HVI到RGB逆转换
        rgb_reconstructed = transform.hvi_to_rgb(hvi_image)
        print(f"✅ HVI到RGB转换成功: {rgb_reconstructed.shape}")
        
        # 计算重建误差
        mse = torch.mean((rgb_image - rgb_reconstructed) ** 2)
        mae = torch.mean(torch.abs(rgb_image - rgb_reconstructed))
        print(f"✅ 重建误差 - MSE: {mse:.6f}, MAE: {mae:.6f}")
        
        # 方法2：使用函数式接口
        hvi_func = rgb_to_hvi(rgb_image, density_k=0.2)
        rgb_func = hvi_to_rgb(hvi_func, density_k=0.2)
        print("✅ 函数式接口测试成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本使用演示失败: {e}")
        return False

def demo_registry_usage():
    """演示注册表系统使用"""
    print("\n=== 注册表系统使用演示 ===")
    
    try:
        from src.llie.utils.registry import MODELS
        
        # 检查组件是否已注册
        if "HVITransform" in MODELS._registry:
            print("✅ HVITransform已在注册表中")
            
            # 通过注册表创建实例
            transform_class = MODELS.get("HVITransform")
            transform = transform_class(density_k=0.3, learnable_k=True)
            print("✅ 通过注册表创建实例成功")
            
            # 使用build方法创建
            transform2 = MODELS.build("HVITransform", density_k=0.5, learnable_k=False)
            print("✅ 使用build方法创建实例成功")
            
            # 验证参数设置
            print(f"   transform1 density_k: {transform.density_k.item():.3f}")
            print(f"   transform2 density_k: {transform2.density_k.item():.3f}")
            
            return True
        else:
            print("❌ HVITransform未在注册表中找到")
            print(f"   已注册的模型: {list(MODELS._registry.keys())}")
            return False
            
    except Exception as e:
        print(f"❌ 注册表使用演示失败: {e}")
        return False

def demo_model_integration():
    """演示在模型中集成HVI转换"""
    print("\n=== 模型集成演示 ===")
    
    try:
        from LLIE.src.llie.models.components.HVITransform import HVITransform
        
        class HVIEnhancedModel(nn.Module):
            """集成HVI转换的示例模型"""
            
            def __init__(self, use_hvi=True, density_k=0.2):
                super().__init__()
                
                self.use_hvi = use_hvi
                if use_hvi:
                    self.hvi_transform = HVITransform(density_k=density_k, learnable_k=True)
                
                # 简单的处理网络
                self.processor = nn.Sequential(
                    nn.Conv2d(3, 32, 3, padding=1),
                    nn.ReLU(),
                    nn.Conv2d(32, 32, 3, padding=1),
                    nn.ReLU(),
                    nn.Conv2d(32, 3, 3, padding=1),
                    nn.Sigmoid()
                )
            
            def forward(self, x):
                if self.use_hvi:
                    # 转换到HVI空间
                    hvi = self.hvi_transform.rgb_to_hvi(x)
                    
                    # 在HVI空间处理
                    processed_hvi = self.processor(hvi)
                    
                    # 转换回RGB空间
                    result = self.hvi_transform.hvi_to_rgb(processed_hvi)
                else:
                    # 直接在RGB空间处理
                    result = self.processor(x)
                
                return result
        
        # 创建模型实例
        model_hvi = HVIEnhancedModel(use_hvi=True)
        model_rgb = HVIEnhancedModel(use_hvi=False)
        print("✅ 创建集成HVI的模型成功")
        
        # 测试前向传播
        test_input = torch.rand(1, 3, 64, 64)
        
        output_hvi = model_hvi(test_input)
        output_rgb = model_rgb(test_input)
        
        print(f"✅ HVI模型输出形状: {output_hvi.shape}")
        print(f"✅ RGB模型输出形状: {output_rgb.shape}")
        
        # 比较参数数量
        params_hvi = sum(p.numel() for p in model_hvi.parameters())
        params_rgb = sum(p.numel() for p in model_rgb.parameters())
        
        print(f"   HVI模型参数数量: {params_hvi:,}")
        print(f"   RGB模型参数数量: {params_rgb:,}")
        print(f"   HVI额外参数: {params_hvi - params_rgb}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型集成演示失败: {e}")
        return False

def demo_color_analysis():
    """演示色彩分析功能"""
    print("\n=== 色彩分析演示 ===")
    
    try:
        from LLIE.src.llie.models.components.HVITransform import HVITransform
        
        transform = HVITransform(density_k=0.2, learnable_k=False)
        
        # 定义测试颜色
        test_colors = {
            "红色": [1.0, 0.0, 0.0],
            "绿色": [0.0, 1.0, 0.0],
            "蓝色": [0.0, 0.0, 1.0],
            "黄色": [1.0, 1.0, 0.0],
            "洋红": [1.0, 0.0, 1.0],
            "青色": [0.0, 1.0, 1.0],
            "白色": [1.0, 1.0, 1.0],
            "黑色": [0.0, 0.0, 0.0],
            "灰色": [0.5, 0.5, 0.5],
        }
        
        print("颜色转换分析:")
        print("-" * 70)
        print(f"{'颜色':^8} {'RGB':^20} {'H':^8} {'V':^8} {'I':^8} {'重建误差':^10}")
        print("-" * 70)
        
        for color_name, rgb_values in test_colors.items():
            # 创建颜色张量
            rgb_tensor = torch.tensor(rgb_values).float().view(1, 3, 1, 1)
            
            # 转换到HVI
            hvi_tensor = transform.rgb_to_hvi(rgb_tensor)
            H = hvi_tensor[0, 0, 0, 0].item()
            V = hvi_tensor[0, 1, 0, 0].item()
            I = hvi_tensor[0, 2, 0, 0].item()
            
            # 重建RGB
            rgb_reconstructed = transform.hvi_to_rgb(hvi_tensor)
            error = torch.mean(torch.abs(rgb_tensor - rgb_reconstructed)).item()
            
            rgb_str = f"[{rgb_values[0]:.1f},{rgb_values[1]:.1f},{rgb_values[2]:.1f}]"
            print(f"{color_name:^8} {rgb_str:^20} {H:^8.3f} {V:^8.3f} {I:^8.3f} {error:^10.6f}")
        
        print("-" * 70)
        print("✅ 色彩分析完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 色彩分析演示失败: {e}")
        return False

def main():
    """主演示函数"""
    print("HVI色彩空间转换模块使用示例")
    print("=" * 60)
    
    demos = [
        ("基本使用方法", demo_basic_usage),
        ("注册表系统使用", demo_registry_usage),
        ("模型集成", demo_model_integration),
        ("色彩分析", demo_color_analysis),
    ]
    
    passed = 0
    total = len(demos)
    
    for demo_name, demo_func in demos:
        try:
            if demo_func():
                passed += 1
                print(f"✅ {demo_name} 演示成功")
            else:
                print(f"❌ {demo_name} 演示失败")
        except Exception as e:
            print(f"❌ {demo_name} 演示异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"演示结果: {passed}/{total} 成功")
    
    if passed == total:
        print("🎉 所有演示成功！HVI色彩空间转换模块工作正常！")
        print("\n下一步建议:")
        print("1. 在实际的低光图像增强任务中使用HVI转换")
        print("2. 尝试调整density_k参数优化效果")
        print("3. 结合其他LLIE组件构建完整模型")
        return True
    else:
        print("⚠️  部分演示失败，请检查环境配置")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
