"""
LLIE项目统一入口点

这个脚本提供了训练、评估和推理的统一接口，使用Hydra进行配置管理。

运行任务的命令行格式：
    python main.py task=<task_name>

示例：
    - 训练：
      python main.py task=train
    - 评估：
      python main.py task=evaluate evaluation.checkpoint_path=/path/to/checkpoint.pth
    - 推理：
      python main.py task=inference inference.checkpoint_path=/path/to/checkpoint.pth inference.input_path=/path/to/images
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import hydra
from omegaconf import DictConfig, OmegaConf
from loguru import logger

from src.llie.utils.logging import ExperimentLogger


@hydra.main(config_path="configs", config_name="config", version_base=None)
def main(cfg: DictConfig) -> None:
    """
    主函数，根据配置协调任务执行

    Args:
        cfg: Hydra提供的配置对象
    """
    # --- 设置日志 ---
    # 使用简单的控制台日志，让Hydra处理文件日志
    logger.info("🚀 启动LLIE应用程序")

    logger.info(f"当前工作目录: {os.getcwd()}")
    logger.info("配置信息:\n" + OmegaConf.to_yaml(cfg))

    # --- 动态导入并运行指定的任务 ---
    task_name = cfg.task.task
    if not task_name:
        logger.error(
            "配置中未指定任务。请设置'task'参数（例如：train、evaluate、inference）。"
        )
        sys.exit(1)

    try:
        # 动态导入并实例化任务类
        if task_name == "train":
            from src.llie.tasks.train_task import TrainTask

            task = TrainTask(cfg)
            task.run()
        elif task_name == "evaluate":
            from src.llie.tasks.evaluate_task import run

            run(cfg)
        elif task_name == "inference":
            from src.llie.tasks.inference_task import run

            run(cfg)
        else:
            logger.error(f"未知任务: {task_name}")
            sys.exit(1)

        logger.info(f"✅ 任务 '{task_name}' 成功完成。")

    except Exception as e:
        logger.error(f"❌ 任务 '{task_name}' 执行失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
