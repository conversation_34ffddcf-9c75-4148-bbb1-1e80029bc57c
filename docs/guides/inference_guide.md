# 模型测试和推理指南

本指南详细介绍如何使用训练好的LLIE模型进行推理测试，包括单张图像处理、批量处理、结果可视化和性能优化等内容。

## 📋 目录

1. [推理环境准备](#推理环境准备)
2. [单张图像推理](#单张图像推理)
3. [批量图像处理](#批量图像处理)
4. [结果可视化](#结果可视化)
5. [性能优化](#性能优化)
6. [模型部署](#模型部署)
7. [常见问题解决](#常见问题解决)
8. [高级推理技巧](#高级推理技巧)

## 🚀 推理环境准备

### 环境检查

```bash
# 1. 验证环境配置
python -c "
import torch
import torchvision
from PIL import Image
import numpy as np
print('✅ 推理环境检查通过')
print(f'PyTorch版本: {torch.__version__}')
print(f'CUDA可用: {torch.cuda.is_available()}')
"

# 2. 检查模型文件
ls -la outputs/checkpoints/
```

### 模型加载测试

```python
# scripts/test_model_loading.py
import torch
from src.llie.models import build_model
from omegaconf import OmegaConf

def test_model_loading():
    """测试模型加载"""
    
    # 加载配置
    cfg = OmegaConf.load('configs/model/dmfourllie.yaml')
    
    # 创建模型
    model = build_model(cfg)
    
    # 加载权重
    checkpoint_path = 'outputs/checkpoints/best_model.pth'
    if torch.cuda.is_available():
        checkpoint = torch.load(checkpoint_path)
    else:
        checkpoint = torch.load(checkpoint_path, map_location='cpu')
    
    model.load_state_dict(checkpoint['state_dict'])
    model.eval()
    
    print("✅ 模型加载成功")
    print(f"模型参数量: {sum(p.numel() for p in model.parameters()) / 1e6:.2f}M")
    
    # 测试推理
    dummy_input = torch.randn(1, 3, 256, 256)
    with torch.no_grad():
        output = model(dummy_input)
    
    print(f"输入形状: {dummy_input.shape}")
    print(f"输出形状: {output.shape}")
    
    return model

if __name__ == "__main__":
    model = test_model_loading()
```

### 推理配置文件

```yaml
# configs/inference/default.yaml
# 推理基础配置
model:
  checkpoint_path: "outputs/checkpoints/best_model.pth"  # 模型权重路径
  device: "auto"                                         # 设备选择
  half_precision: false                                  # 半精度推理

# 输入配置
input:
  image_size: null                                       # 输入尺寸（null表示保持原尺寸）
  normalize: true                                        # 是否标准化
  
# 输出配置
output:
  save_format: "png"                                     # 保存格式
  quality: 95                                            # JPEG质量（如果使用JPEG）
  
# 批处理配置
batch:
  batch_size: 4                                          # 批次大小
  num_workers: 4                                         # 数据加载进程数
  
# 后处理配置
postprocess:
  denormalize: true                                      # 反标准化
  clip_values: true                                      # 裁剪到[0,1]
  gamma_correction: false                                # Gamma校正
  
# 测试时增强（TTA）
tta:
  enabled: false                                         # 是否启用TTA
  flips: [false, true]                                   # 水平翻转
  rotations: [0]                                         # 旋转角度
```

## 🖼️ 单张图像推理

### 基础推理脚本

```python
# scripts/single_image_inference.py
import torch
import torch.nn.functional as F
from PIL import Image
import numpy as np
from pathlib import Path
import argparse

from src.llie.models import build_model
from src.llie.data.transforms import get_inference_transforms
from omegaconf import OmegaConf

class SingleImageInference:
    """单张图像推理器"""
    
    def __init__(self, model_config_path, checkpoint_path, device='auto'):
        """
        初始化推理器
        
        Args:
            model_config_path: 模型配置文件路径
            checkpoint_path: 模型权重路径
            device: 设备选择
        """
        self.device = self._setup_device(device)
        self.model = self._load_model(model_config_path, checkpoint_path)
        self.transforms = get_inference_transforms()
        
    def _setup_device(self, device):
        """设置设备"""
        if device == 'auto':
            return torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        return torch.device(device)
    
    def _load_model(self, config_path, checkpoint_path):
        """加载模型"""
        # 加载配置
        cfg = OmegaConf.load(config_path)
        
        # 创建模型
        model = build_model(cfg)
        
        # 加载权重
        checkpoint = torch.load(checkpoint_path, map_location=self.device)
        
        # 处理不同的检查点格式
        if 'state_dict' in checkpoint:
            state_dict = checkpoint['state_dict']
        else:
            state_dict = checkpoint
        
        # 移除可能的模块前缀
        new_state_dict = {}
        for key, value in state_dict.items():
            new_key = key.replace('model.', '') if key.startswith('model.') else key
            new_state_dict[new_key] = value
        
        model.load_state_dict(new_state_dict)
        model.to(self.device)
        model.eval()
        
        print(f"✅ 模型加载成功，设备: {self.device}")
        return model
    
    def preprocess_image(self, image_path):
        """预处理图像"""
        # 读取图像
        image = Image.open(image_path).convert('RGB')
        original_size = image.size
        
        # 转换为numpy数组
        image_np = np.array(image) / 255.0
        
        # 应用变换
        transformed = self.transforms(image=image_np)
        image_tensor = transformed['image'].unsqueeze(0).to(self.device)
        
        return image_tensor, original_size
    
    def postprocess_output(self, output_tensor, original_size):
        """后处理输出"""
        # 移到CPU并转换为numpy
        output_np = output_tensor.squeeze(0).cpu().numpy()
        
        # 反标准化（如果需要）
        # 这里假设模型输出已经在[0,1]范围内
        output_np = np.clip(output_np, 0, 1)
        
        # 转换通道顺序 (C, H, W) -> (H, W, C)
        output_np = output_np.transpose(1, 2, 0)
        
        # 转换为PIL图像
        output_image = Image.fromarray((output_np * 255).astype(np.uint8))
        
        # 调整到原始尺寸（如果需要）
        if output_image.size != original_size:
            output_image = output_image.resize(original_size, Image.LANCZOS)
        
        return output_image
    
    def enhance_image(self, input_path, output_path=None):
        """增强单张图像"""
        input_path = Path(input_path)
        
        if output_path is None:
            output_path = input_path.parent / f"{input_path.stem}_enhanced{input_path.suffix}"
        
        print(f"🔄 处理图像: {input_path}")
        
        # 预处理
        input_tensor, original_size = self.preprocess_image(input_path)
        
        # 推理
        with torch.no_grad():
            output_tensor = self.model(input_tensor)
        
        # 后处理
        enhanced_image = self.postprocess_output(output_tensor, original_size)
        
        # 保存结果
        enhanced_image.save(output_path)
        print(f"✅ 增强完成，保存到: {output_path}")
        
        return enhanced_image

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='单张图像低光增强')
    parser.add_argument('--input', '-i', required=True, help='输入图像路径')
    parser.add_argument('--output', '-o', help='输出图像路径')
    parser.add_argument('--model_config', default='configs/model/dmfourllie.yaml', help='模型配置文件')
    parser.add_argument('--checkpoint', default='outputs/checkpoints/best_model.pth', help='模型权重文件')
    parser.add_argument('--device', default='auto', help='设备选择')
    
    args = parser.parse_args()
    
    # 创建推理器
    inferencer = SingleImageInference(
        model_config_path=args.model_config,
        checkpoint_path=args.checkpoint,
        device=args.device
    )
    
    # 执行推理
    enhanced_image = inferencer.enhance_image(args.input, args.output)
    
    print("🎉 推理完成！")

if __name__ == "__main__":
    main()
```

### 使用命令行进行推理

```bash
# 基础用法
python scripts/single_image_inference.py --input data/test_image.png

# 指定输出路径
python scripts/single_image_inference.py \
  --input data/test_image.png \
  --output results/enhanced_image.png

# 使用CPU推理
python scripts/single_image_inference.py \
  --input data/test_image.png \
  --device cpu

# 使用自定义模型
python scripts/single_image_inference.py \
  --input data/test_image.png \
  --checkpoint outputs/checkpoints/epoch_50.pth
```

### 集成到Hydra配置系统

```bash
# 使用Hydra配置进行推理
python run.py task=inference \
  inference.input_path=data/test_image.png \
  inference.output_path=results/enhanced.png \
  inference.checkpoint_path=outputs/checkpoints/best_model.pth
```

## 📦 批量图像处理

### 批量处理脚本

```python
# scripts/batch_inference.py
import torch
from torch.utils.data import Dataset, DataLoader
from PIL import Image
import numpy as np
from pathlib import Path
from tqdm import tqdm
import argparse

class ImageDataset(Dataset):
    """图像数据集（用于批量推理）"""
    
    def __init__(self, image_paths, transforms=None):
        self.image_paths = image_paths
        self.transforms = transforms
    
    def __len__(self):
        return len(self.image_paths)
    
    def __getitem__(self, idx):
        image_path = self.image_paths[idx]
        
        # 读取图像
        image = Image.open(image_path).convert('RGB')
        original_size = image.size
        
        # 转换为numpy数组
        image_np = np.array(image) / 255.0
        
        # 应用变换
        if self.transforms:
            transformed = self.transforms(image=image_np)
            image_tensor = transformed['image']
        else:
            image_tensor = torch.from_numpy(image_np.transpose(2, 0, 1)).float()
        
        return {
            'image': image_tensor,
            'path': str(image_path),
            'original_size': original_size
        }

class BatchInference:
    """批量推理器"""
    
    def __init__(self, model_config_path, checkpoint_path, device='auto'):
        self.device = self._setup_device(device)
        self.model = self._load_model(model_config_path, checkpoint_path)
        self.transforms = get_inference_transforms()
    
    def _setup_device(self, device):
        """设置设备"""
        if device == 'auto':
            return torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        return torch.device(device)
    
    def _load_model(self, config_path, checkpoint_path):
        """加载模型（与单张推理相同）"""
        # 实现与SingleImageInference相同的模型加载逻辑
        pass
    
    def process_directory(self, input_dir, output_dir, batch_size=4, num_workers=4):
        """处理整个目录"""
        input_dir = Path(input_dir)
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 获取所有图像文件
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}
        image_paths = [
            p for p in input_dir.rglob('*') 
            if p.suffix.lower() in image_extensions
        ]
        
        if not image_paths:
            print("❌ 未找到图像文件")
            return
        
        print(f"📁 找到 {len(image_paths)} 张图像")
        
        # 创建数据集和数据加载器
        dataset = ImageDataset(image_paths, self.transforms)
        dataloader = DataLoader(
            dataset, 
            batch_size=batch_size, 
            num_workers=num_workers,
            shuffle=False
        )
        
        # 批量处理
        self.model.eval()
        with torch.no_grad():
            for batch in tqdm(dataloader, desc="批量处理"):
                images = batch['image'].to(self.device)
                paths = batch['path']
                original_sizes = batch['original_size']
                
                # 推理
                enhanced_images = self.model(images)
                
                # 保存结果
                for i in range(enhanced_images.size(0)):
                    self._save_enhanced_image(
                        enhanced_images[i],
                        paths[i],
                        original_sizes[i],
                        input_dir,
                        output_dir
                    )
        
        print(f"✅ 批量处理完成，结果保存到: {output_dir}")
    
    def _save_enhanced_image(self, enhanced_tensor, original_path, original_size, input_dir, output_dir):
        """保存增强后的图像"""
        # 后处理
        enhanced_np = enhanced_tensor.cpu().numpy()
        enhanced_np = np.clip(enhanced_np, 0, 1)
        enhanced_np = enhanced_np.transpose(1, 2, 0)
        enhanced_image = Image.fromarray((enhanced_np * 255).astype(np.uint8))
        
        # 调整尺寸
        if enhanced_image.size != tuple(original_size):
            enhanced_image = enhanced_image.resize(tuple(original_size), Image.LANCZOS)
        
        # 计算输出路径（保持目录结构）
        relative_path = Path(original_path).relative_to(input_dir)
        output_path = output_dir / relative_path
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 保存
        enhanced_image.save(output_path)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='批量图像低光增强')
    parser.add_argument('--input_dir', '-i', required=True, help='输入目录')
    parser.add_argument('--output_dir', '-o', required=True, help='输出目录')
    parser.add_argument('--batch_size', '-b', type=int, default=4, help='批次大小')
    parser.add_argument('--num_workers', '-w', type=int, default=4, help='数据加载进程数')
    parser.add_argument('--model_config', default='configs/model/dmfourllie.yaml', help='模型配置文件')
    parser.add_argument('--checkpoint', default='outputs/checkpoints/best_model.pth', help='模型权重文件')
    parser.add_argument('--device', default='auto', help='设备选择')
    
    args = parser.parse_args()
    
    # 创建批量推理器
    batch_inferencer = BatchInference(
        model_config_path=args.model_config,
        checkpoint_path=args.checkpoint,
        device=args.device
    )
    
    # 执行批量处理
    batch_inferencer.process_directory(
        input_dir=args.input_dir,
        output_dir=args.output_dir,
        batch_size=args.batch_size,
        num_workers=args.num_workers
    )

if __name__ == "__main__":
    main()
```

### 批量处理命令示例

```bash
# 处理整个目录
python scripts/batch_inference.py \
  --input_dir data/test_images \
  --output_dir results/enhanced_images

# 调整批次大小和进程数
python scripts/batch_inference.py \
  --input_dir data/test_images \
  --output_dir results/enhanced_images \
  --batch_size 8 \
  --num_workers 8

# 使用CPU处理（内存不足时）
python scripts/batch_inference.py \
  --input_dir data/test_images \
  --output_dir results/enhanced_images \
  --device cpu \
  --batch_size 2
```

## 📊 结果可视化

### 对比可视化

```python
# scripts/visualize_results.py
import matplotlib.pyplot as plt
import numpy as np
from PIL import Image
from pathlib import Path
import argparse

def create_comparison_grid(low_path, enhanced_path, normal_path=None, save_path=None):
    """创建对比网格图"""
    
    # 读取图像
    low_img = Image.open(low_path)
    enhanced_img = Image.open(enhanced_path)
    
    if normal_path and Path(normal_path).exists():
        normal_img = Image.open(normal_path)
        num_cols = 3
        titles = ['低光图像', '增强结果', '参考图像']
        images = [low_img, enhanced_img, normal_img]
    else:
        num_cols = 2
        titles = ['低光图像', '增强结果']
        images = [low_img, enhanced_img]
    
    # 创建子图
    fig, axes = plt.subplots(1, num_cols, figsize=(5 * num_cols, 5))
    if num_cols == 1:
        axes = [axes]
    
    for i, (img, title) in enumerate(zip(images, titles)):
        axes[i].imshow(img)
        axes[i].set_title(title, fontsize=14, fontweight='bold')
        axes[i].axis('off')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        print(f"✅ 对比图保存到: {save_path}")
    
    plt.show()

def create_batch_comparison(input_dir, enhanced_dir, normal_dir=None, output_path=None, max_images=9):
    """创建批量对比图"""
    
    input_dir = Path(input_dir)
    enhanced_dir = Path(enhanced_dir)
    
    # 获取图像文件
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp'}
    input_images = [p for p in input_dir.glob('*') if p.suffix.lower() in image_extensions]
    input_images = input_images[:max_images]
    
    if not input_images:
        print("❌ 未找到图像文件")
        return
    
    # 计算网格尺寸
    n_images = len(input_images)
    n_cols = 3 if normal_dir else 2
    n_rows = n_images
    
    fig, axes = plt.subplots(n_rows, n_cols, figsize=(5 * n_cols, 4 * n_rows))
    if n_rows == 1:
        axes = axes.reshape(1, -1)
    
    for i, input_path in enumerate(input_images):
        # 读取图像
        low_img = Image.open(input_path)
        enhanced_path = enhanced_dir / input_path.name
        
        if enhanced_path.exists():
            enhanced_img = Image.open(enhanced_path)
        else:
            enhanced_img = low_img  # 如果增强图像不存在，使用原图
        
        # 显示低光图像
        axes[i, 0].imshow(low_img)
        axes[i, 0].set_title(f'低光 - {input_path.name}')
        axes[i, 0].axis('off')
        
        # 显示增强图像
        axes[i, 1].imshow(enhanced_img)
        axes[i, 1].set_title(f'增强 - {input_path.name}')
        axes[i, 1].axis('off')
        
        # 显示参考图像（如果有）
        if normal_dir:
            normal_path = Path(normal_dir) / input_path.name
            if normal_path.exists():
                normal_img = Image.open(normal_path)
                axes[i, 2].imshow(normal_img)
                axes[i, 2].set_title(f'参考 - {input_path.name}')
                axes[i, 2].axis('off')
    
    plt.tight_layout()
    
    if output_path:
        plt.savefig(output_path, dpi=150, bbox_inches='tight')
        print(f"✅ 批量对比图保存到: {output_path}")
    
    plt.show()

def analyze_brightness_distribution(image_paths, save_path=None):
    """分析图像亮度分布"""
    
    brightness_values = []
    
    for img_path in image_paths:
        img = Image.open(img_path).convert('L')  # 转换为灰度图
        img_array = np.array(img)
        brightness_values.append(np.mean(img_array))
    
    # 绘制亮度分布
    plt.figure(figsize=(10, 6))
    plt.hist(brightness_values, bins=30, alpha=0.7, edgecolor='black')
    plt.xlabel('平均亮度值')
    plt.ylabel('图像数量')
    plt.title('图像亮度分布')
    plt.grid(True, alpha=0.3)
    
    # 添加统计信息
    mean_brightness = np.mean(brightness_values)
    std_brightness = np.std(brightness_values)
    plt.axvline(mean_brightness, color='red', linestyle='--', 
                label=f'平均值: {mean_brightness:.1f}')
    plt.axvline(mean_brightness + std_brightness, color='orange', linestyle='--', 
                label=f'+1σ: {mean_brightness + std_brightness:.1f}')
    plt.axvline(mean_brightness - std_brightness, color='orange', linestyle='--', 
                label=f'-1σ: {mean_brightness - std_brightness:.1f}')
    
    plt.legend()
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        print(f"✅ 亮度分布图保存到: {save_path}")
    
    plt.show()
    
    return brightness_values

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='结果可视化')
    parser.add_argument('--mode', choices=['single', 'batch', 'brightness'], 
                       default='single', help='可视化模式')
    parser.add_argument('--low_path', help='低光图像路径')
    parser.add_argument('--enhanced_path', help='增强图像路径')
    parser.add_argument('--normal_path', help='参考图像路径')
    parser.add_argument('--input_dir', help='输入目录')
    parser.add_argument('--enhanced_dir', help='增强结果目录')
    parser.add_argument('--normal_dir', help='参考图像目录')
    parser.add_argument('--output', help='输出图像路径')
    parser.add_argument('--max_images', type=int, default=9, help='最大显示图像数')
    
    args = parser.parse_args()
    
    if args.mode == 'single':
        create_comparison_grid(
            args.low_path, args.enhanced_path, 
            args.normal_path, args.output
        )
    elif args.mode == 'batch':
        create_batch_comparison(
            args.input_dir, args.enhanced_dir, 
            args.normal_dir, args.output, args.max_images
        )
    elif args.mode == 'brightness':
        image_paths = list(Path(args.input_dir).glob('*.png'))
        analyze_brightness_distribution(image_paths, args.output)

if __name__ == "__main__":
    main()
```

### 可视化命令示例

```bash
# 单张图像对比
python scripts/visualize_results.py \
  --mode single \
  --low_path data/test/low/1.png \
  --enhanced_path results/enhanced/1.png \
  --normal_path data/test/normal/1.png \
  --output comparison.png

# 批量对比
python scripts/visualize_results.py \
  --mode batch \
  --input_dir data/test/low \
  --enhanced_dir results/enhanced \
  --normal_dir data/test/normal \
  --output batch_comparison.png

# 亮度分布分析
python scripts/visualize_results.py \
  --mode brightness \
  --input_dir results/enhanced \
  --output brightness_distribution.png
```

## ⚡ 性能优化

### 推理加速技巧

```python
# 推理优化配置
class OptimizedInference:
    """优化的推理器"""
    
    def __init__(self, model_config_path, checkpoint_path, optimization_level='medium'):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = self._load_and_optimize_model(
            model_config_path, checkpoint_path, optimization_level
        )
    
    def _load_and_optimize_model(self, config_path, checkpoint_path, optimization_level):
        """加载并优化模型"""
        # 基础模型加载
        model = self._load_model(config_path, checkpoint_path)
        
        if optimization_level == 'low':
            # 基础优化
            model.eval()
            
        elif optimization_level == 'medium':
            # 中等优化
            model.eval()
            if torch.cuda.is_available():
                model = model.half()  # 半精度
            
        elif optimization_level == 'high':
            # 高级优化
            model.eval()
            if torch.cuda.is_available():
                model = model.half()  # 半精度
                # 可以添加TensorRT优化等
        
        return model
    
    @torch.jit.script
    def _optimized_forward(self, x):
        """JIT编译的前向传播"""
        # 这里可以添加模型特定的优化
        pass
```

### 内存优化

```python
# 内存优化策略
def memory_efficient_inference(model, image_tensor, tile_size=512, overlap=64):
    """内存高效的推理（适用于大图像）"""
    
    _, _, H, W = image_tensor.shape
    
    if H <= tile_size and W <= tile_size:
        # 图像足够小，直接推理
        with torch.no_grad():
            return model(image_tensor)
    
    # 大图像分块处理
    output = torch.zeros_like(image_tensor)
    
    for y in range(0, H, tile_size - overlap):
        for x in range(0, W, tile_size - overlap):
            # 计算块的边界
            y_end = min(y + tile_size, H)
            x_end = min(x + tile_size, W)
            
            # 提取块
            tile = image_tensor[:, :, y:y_end, x:x_end]
            
            # 推理
            with torch.no_grad():
                enhanced_tile = model(tile)
            
            # 处理重叠区域
            if overlap > 0 and (y > 0 or x > 0):
                # 使用渐变融合处理重叠
                enhanced_tile = blend_tiles(enhanced_tile, overlap)
            
            # 放回输出
            output[:, :, y:y_end, x:x_end] = enhanced_tile
    
    return output

def blend_tiles(tile, overlap):
    """融合重叠区域"""
    # 实现渐变融合逻辑
    pass
```

### 批处理优化

```python
# 动态批处理
class DynamicBatchInference:
    """动态批处理推理"""
    
    def __init__(self, model, max_batch_size=8, target_memory_usage=0.8):
        self.model = model
        self.max_batch_size = max_batch_size
        self.target_memory_usage = target_memory_usage
        self.optimal_batch_size = self._find_optimal_batch_size()
    
    def _find_optimal_batch_size(self):
        """找到最优批次大小"""
        if not torch.cuda.is_available():
            return 1
        
        # 测试不同批次大小的内存使用
        for batch_size in range(1, self.max_batch_size + 1):
            try:
                dummy_input = torch.randn(batch_size, 3, 256, 256).cuda()
                
                torch.cuda.empty_cache()
                torch.cuda.reset_peak_memory_stats()
                
                with torch.no_grad():
                    _ = self.model(dummy_input)
                
                peak_memory = torch.cuda.max_memory_allocated()
                total_memory = torch.cuda.get_device_properties(0).total_memory
                memory_usage = peak_memory / total_memory
                
                if memory_usage > self.target_memory_usage:
                    return max(1, batch_size - 1)
                    
            except RuntimeError as e:
                if "out of memory" in str(e):
                    return max(1, batch_size - 1)
                raise e
        
        return self.max_batch_size
    
    def process_images(self, image_tensors):
        """处理图像列表"""
        results = []
        
        for i in range(0, len(image_tensors), self.optimal_batch_size):
            batch = image_tensors[i:i + self.optimal_batch_size]
            batch_tensor = torch.stack(batch)
            
            with torch.no_grad():
                batch_output = self.model(batch_tensor)
            
            results.extend(torch.unbind(batch_output, dim=0))
        
        return results
```

---

本指南涵盖了LLIE项目中模型推理的各个方面，从基础的单张图像处理到高级的性能优化技巧。通过这些工具和技术，您可以高效地使用训练好的模型进行低光图像增强。
