# LLIE项目环境配置指南

本文档详细介绍如何配置LLIE（低光图像增强）项目的开发和运行环境。

## 环境要求

### 系统要求
- **操作系统**: Linux (推荐 Ubuntu 18.04+), macOS, Windows 10+
- **Python版本**: Python 3.8 - 3.11
- **GPU**: NVIDIA GPU (推荐，支持CUDA 11.0+)
- **内存**: 至少8GB RAM (推荐16GB+)
- **存储**: 至少10GB可用空间

### 软件依赖
- **Conda**: Anaconda或Miniconda
- **CUDA**: 11.0+ (如果使用GPU)
- **Git**: 用于版本控制

## 环境安装步骤

### 1. 安装Conda

如果您还没有安装Conda，请按照以下步骤：

#### Linux/macOS:
```bash
# 下载Miniconda
wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh

# 安装
bash Miniconda3-latest-Linux-x86_64.sh

# 重新加载shell配置
source ~/.bashrc
```

#### Windows:
1. 下载Miniconda安装程序：https://docs.conda.io/en/latest/miniconda.html
2. 运行安装程序并按照提示完成安装
3. 打开Anaconda Prompt

### 2. 创建LLIE环境

```bash
# 创建名为'llie'的conda环境
conda create -n llie python=3.9 -y

# 激活环境
conda activate llie
```

### 3. 安装PyTorch

根据您的系统配置选择合适的PyTorch版本：

#### GPU版本 (推荐):
```bash
# CUDA 11.8
conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia

# CUDA 12.1
conda install pytorch torchvision torchaudio pytorch-cuda=12.1 -c pytorch -c nvidia
```

#### CPU版本:
```bash
conda install pytorch torchvision torchaudio cpuonly -c pytorch
```

### 4. 安装项目依赖

```bash
# 进入项目目录
cd /path/to/LLIE

# 安装项目依赖
pip install -r requirements.txt

# 以开发模式安装项目
pip install -e .
```

### 5. 验证安装

```bash
# 验证PyTorch安装
python -c "import torch; print(f'PyTorch版本: {torch.__version__}')"
python -c "import torch; print(f'CUDA可用: {torch.cuda.is_available()}')"

# 验证项目安装
python -c "from src.llie.models import LLIE; print('模型导入成功')"

# 运行配置验证
python run.py --help
```

## 环境管理

### 激活环境
每次使用项目前，都需要激活conda环境：

```bash
conda activate llie
```

### 停用环境
```bash
conda deactivate
```

### 查看环境信息
```bash
# 查看当前环境
conda info --envs

# 查看已安装的包
conda list

# 查看pip安装的包
pip list
```

### 更新环境
```bash
# 更新conda
conda update conda

# 更新环境中的包
conda update --all

# 更新特定包
pip install --upgrade package_name
```

## 数据准备

### 1. 创建数据目录
```bash
mkdir -p data/LOLv2/Real_captured/{Train,Test}/{Low,Normal}
```

### 2. 下载LOL数据集
```bash
# 下载LOLv2数据集
# 请从官方网站下载数据集文件
# 解压到相应目录
```

### 3. 验证数据结构
```bash
# 检查数据目录结构
tree data/ -L 4
```

期望的目录结构：
```
data/
└── LOLv2/
    └── Real_captured/
        ├── Train/
        │   ├── Low/
        │   └── Normal/
        └── Test/
            ├── Low/
            └── Normal/
```

## 开发工具配置

### 1. Jupyter Notebook
```bash
# 安装Jupyter
conda install jupyter -y

# 启动Jupyter
jupyter notebook
```

### 2. VS Code配置
1. 安装Python扩展
2. 选择正确的Python解释器：
   - 按Ctrl+Shift+P
   - 输入"Python: Select Interpreter"
   - 选择conda环境中的Python

### 3. Git配置
```bash
# 配置Git用户信息
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"

# 初始化Git仓库（如果需要）
git init
git add .
git commit -m "Initial commit"
```

## 常见问题解决

### 1. CUDA相关问题

**问题**: `RuntimeError: CUDA out of memory`
**解决方案**:
```bash
# 减小批次大小
python run.py trainer.batch_size=4

# 启用混合精度训练
python run.py trainer.use_amp=true
```

**问题**: CUDA版本不匹配
**解决方案**:
```bash
# 检查CUDA版本
nvidia-smi

# 重新安装匹配的PyTorch版本
conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia
```

### 2. 依赖冲突

**问题**: 包版本冲突
**解决方案**:
```bash
# 创建新的干净环境
conda create -n llie_new python=3.9 -y
conda activate llie_new

# 重新安装依赖
pip install -r requirements.txt
```

### 3. 内存不足

**问题**: 训练时内存不足
**解决方案**:
```bash
# 减少数据加载进程数
python run.py num_workers=2

# 禁用图像缓存
python run.py dataset.cache_images=false
```

### 4. 权限问题

**问题**: 无法写入输出目录
**解决方案**:
```bash
# 检查目录权限
ls -la outputs/

# 修改权限
chmod 755 outputs/
```

## 性能优化建议

### 1. 硬件优化
- 使用SSD存储数据集
- 确保足够的RAM
- 使用多核CPU进行数据加载

### 2. 软件优化
```bash
# 设置环境变量优化性能
export OMP_NUM_THREADS=4
export MKL_NUM_THREADS=4

# 启用CUDA优化
export CUDA_LAUNCH_BLOCKING=0
```

### 3. 训练优化
```bash
# 使用混合精度训练
python run.py trainer.use_amp=true

# 增加数据加载进程
python run.py num_workers=8

# 启用数据缓存（如果内存充足）
python run.py dataset.cache_images=true
```

## 环境备份和恢复

### 备份环境
```bash
# 导出环境配置
conda env export > environment.yml

# 导出pip依赖
pip freeze > requirements_freeze.txt
```

### 恢复环境
```bash
# 从配置文件创建环境
conda env create -f environment.yml

# 或者使用pip安装
pip install -r requirements_freeze.txt
```

## 多环境管理

如果需要管理多个版本的环境：

```bash
# 创建不同版本的环境
conda create -n llie_dev python=3.9 -y
conda create -n llie_prod python=3.8 -y

# 切换环境
conda activate llie_dev
conda activate llie_prod

# 删除不需要的环境
conda env remove -n old_env_name
```

## 联系和支持

如果在环境配置过程中遇到问题：

1. 检查本文档的常见问题部分
2. 查看项目的GitHub Issues
3. 确保按照步骤正确执行每个命令
4. 检查系统兼容性和硬件要求

记住：始终在激活的conda环境中运行项目相关命令！
