# 完整项目构建指南

本指南详细介绍如何从零开始构建一个类似LLIE的深度学习计算机视觉项目，包括项目架构设计、核心组件实现、配置系统搭建等完整流程。

## 📋 目录

1. [项目架构设计](#项目架构设计)
2. [环境和依赖配置](#环境和依赖配置)
3. [核心系统实现](#核心系统实现)
4. [数据处理系统](#数据处理系统)
5. [模型架构系统](#模型架构系统)
6. [训练和评估系统](#训练和评估系统)
7. [配置管理系统](#配置管理系统)
8. [项目集成和测试](#项目集成和测试)

## 🏗️ 项目架构设计

### 整体架构规划

```
my_cv_project/
├── configs/                    # 配置文件目录
│   ├── config.yaml            # 主配置文件
│   ├── model/                 # 模型配置
│   ├── dataset/               # 数据集配置
│   ├── loss/                  # 损失函数配置
│   └── experiment/            # 实验配置
├── src/                       # 源代码目录
│   └── my_project/
│       ├── __init__.py
│       ├── models/            # 模型定义
│       ├── data/              # 数据处理
│       ├── losses/            # 损失函数
│       ├── utils/             # 工具函数
│       ├── tasks/             # 任务定义
│       └── engine/            # 训练引擎
├── scripts/                   # 脚本目录
├── docs/                      # 文档目录
├── tests/                     # 测试目录
├── data/                      # 数据目录
├── outputs/                   # 输出目录
├── requirements.txt           # 依赖列表
├── setup.py                   # 安装脚本
├── run.py                     # 主运行脚本
└── README.md                  # 项目说明
```

### 设计原则

1. **模块化设计**: 每个功能模块独立，便于维护和扩展
2. **配置驱动**: 通过配置文件控制所有参数，避免硬编码
3. **注册表模式**: 使用注册表实现组件的即插即用
4. **可扩展性**: 易于添加新的模型、数据集、损失函数等
5. **可复现性**: 确保实验结果可重复
6. **文档完整**: 提供详细的文档和示例

## 🔧 环境和依赖配置

### 1. 创建项目环境

```bash
# 创建项目目录
mkdir my_cv_project
cd my_cv_project

# 创建conda环境
conda create -n my_cv_project python=3.9
conda activate my_cv_project

# 创建基础目录结构
mkdir -p src/my_project/{models,data,losses,utils,tasks,engine}
mkdir -p configs/{model,dataset,loss,experiment}
mkdir -p scripts docs tests data outputs
touch src/my_project/__init__.py
```

### 2. 依赖管理

```python
# requirements.txt
torch>=1.12.0
torchvision>=0.13.0
numpy>=1.21.0
opencv-python>=4.5.0
Pillow>=8.3.0
matplotlib>=3.5.0
seaborn>=0.11.0
tqdm>=4.62.0
loguru>=0.6.0
rich>=12.0.0
hydra-core>=1.2.0
omegaconf>=2.2.0
wandb>=0.13.0
albumentations>=1.3.0
scikit-learn>=1.1.0
scipy>=1.8.0
lpips>=0.1.4
tensorboard>=2.8.0
pytest>=7.0.0
black>=22.0.0
isort>=5.10.0
flake8>=4.0.0
```

### 3. 项目安装配置

```python
# setup.py
from setuptools import setup, find_packages

setup(
    name="my-cv-project",
    version="0.1.0",
    description="深度学习计算机视觉项目模板",
    author="Your Name",
    author_email="<EMAIL>",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    python_requires=">=3.8",
    install_requires=[
        "torch>=1.12.0",
        "torchvision>=0.13.0",
        "hydra-core>=1.2.0",
        "omegaconf>=2.2.0",
        "wandb>=0.13.0",
        "loguru>=0.6.0",
        "rich>=12.0.0",
        "albumentations>=1.3.0",
        "opencv-python>=4.5.0",
        "matplotlib>=3.5.0",
        "tqdm>=4.62.0",
        "numpy>=1.21.0",
        "Pillow>=8.3.0",
    ],
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "black>=22.0.0",
            "isort>=5.10.0",
            "flake8>=4.0.0",
        ]
    },
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
    ],
)
```

## 🔧 核心系统实现

### 1. 注册表系统

```python
# src/my_project/utils/registry.py
"""
注册表系统 - 实现组件的即插即用
"""
from typing import Dict, Any, Optional, Callable
from omegaconf import DictConfig, OmegaConf

class Registry:
    """通用注册表类"""
    
    def __init__(self, name: str):
        self.name = name
        self.module_dict: Dict[str, Any] = {}
    
    def register(self, name: Optional[str] = None) -> Callable:
        """注册装饰器"""
        def decorator(cls):
            register_name = name if name is not None else cls.__name__
            
            if register_name in self.module_dict:
                raise KeyError(f'{register_name} 已经在 {self.name} 中注册')
            
            self.module_dict[register_name] = cls
            cls._registry_name = register_name
            cls._registry = self
            
            return cls
        
        return decorator
    
    def build(self, cfg: Any, *args, **kwargs) -> Any:
        """构建组件实例"""
        if isinstance(cfg, str):
            component_name = cfg
            component_args = kwargs
        elif isinstance(cfg, (dict, DictConfig)):
            cfg_dict = cfg if isinstance(cfg, dict) else OmegaConf.to_container(cfg)
            component_name = cfg_dict.pop('type')
            component_args = {**cfg_dict, **kwargs}
        else:
            raise TypeError(f"配置类型不支持: {type(cfg)}")
        
        if component_name not in self.module_dict:
            raise KeyError(f'{component_name} 未在 {self.name} 中注册')
        
        component_cls = self.module_dict[component_name]
        return component_cls(*args, **component_args)
    
    def list_modules(self) -> list:
        """列出所有注册的组件"""
        return list(self.module_dict.keys())
    
    def get(self, name: str) -> Any:
        """获取注册的组件类"""
        return self.module_dict.get(name)

# 创建全局注册表
MODELS = Registry('models')
DATASETS = Registry('datasets')
LOSSES = Registry('losses')
OPTIMIZERS = Registry('optimizers')
SCHEDULERS = Registry('schedulers')
TRANSFORMS = Registry('transforms')
METRICS = Registry('metrics')

# 便捷的注册装饰器
register_model = MODELS.register
register_dataset = DATASETS.register
register_loss = LOSSES.register
register_optimizer = OPTIMIZERS.register
register_scheduler = SCHEDULERS.register
register_transform = TRANSFORMS.register
register_metric = METRICS.register
```

### 2. 基础架构类

```python
# src/my_project/models/base.py
"""
基础模型架构
"""
import torch
import torch.nn as nn
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional

class BaseArchitecture(nn.Module, ABC):
    """基础架构抽象类"""
    
    def __init__(self):
        super().__init__()
        self._model_name = self.__class__.__name__
    
    @abstractmethod
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播 - 子类必须实现"""
        pass
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        return {
            'model_name': self._model_name,
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'model_size_mb': total_params * 4 / (1024 * 1024),  # 假设float32
        }
    
    def print_model_info(self):
        """打印模型信息"""
        info = self.get_model_info()
        print(f"模型名称: {info['model_name']}")
        print(f"总参数量: {info['total_parameters']:,}")
        print(f"可训练参数: {info['trainable_parameters']:,}")
        print(f"模型大小: {info['model_size_mb']:.2f} MB")
    
    def save_checkpoint(self, path: str, epoch: int, optimizer_state: Optional[Dict] = None, 
                       scheduler_state: Optional[Dict] = None, **kwargs):
        """保存检查点"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.state_dict(),
            'model_info': self.get_model_info(),
            **kwargs
        }
        
        if optimizer_state is not None:
            checkpoint['optimizer_state_dict'] = optimizer_state
        
        if scheduler_state is not None:
            checkpoint['scheduler_state_dict'] = scheduler_state
        
        torch.save(checkpoint, path)
    
    def load_checkpoint(self, path: str, map_location: Optional[str] = None) -> Dict:
        """加载检查点"""
        checkpoint = torch.load(path, map_location=map_location)
        self.load_state_dict(checkpoint['model_state_dict'])
        return checkpoint

class BaseComponent(nn.Module):
    """基础组件类"""
    
    def __init__(self, name: Optional[str] = None):
        super().__init__()
        self.component_name = name or self.__class__.__name__
    
    def get_component_info(self) -> Dict[str, Any]:
        """获取组件信息"""
        return {
            'component_name': self.component_name,
            'parameters': sum(p.numel() for p in self.parameters()),
        }
```

### 3. 配置管理系统

```python
# src/my_project/utils/config.py
"""
配置管理系统
"""
import os
from pathlib import Path
from typing import Any, Dict, Optional
from omegaconf import DictConfig, OmegaConf
import hydra
from hydra.core.config_store import ConfigStore

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: str = "configs"):
        self.config_dir = Path(config_dir)
        self.cs = ConfigStore.instance()
    
    def load_config(self, config_path: str) -> DictConfig:
        """加载配置文件"""
        config_file = self.config_dir / config_path
        if not config_file.exists():
            raise FileNotFoundError(f"配置文件不存在: {config_file}")
        
        return OmegaConf.load(config_file)
    
    def merge_configs(self, *configs: DictConfig) -> DictConfig:
        """合并多个配置"""
        merged = OmegaConf.create({})
        for config in configs:
            merged = OmegaConf.merge(merged, config)
        return merged
    
    def save_config(self, config: DictConfig, save_path: str):
        """保存配置文件"""
        save_file = Path(save_path)
        save_file.parent.mkdir(parents=True, exist_ok=True)
        OmegaConf.save(config, save_file)
    
    def register_config(self, config_name: str, config_class: Any):
        """注册配置类"""
        self.cs.store(name=config_name, node=config_class)
    
    def validate_config(self, config: DictConfig) -> bool:
        """验证配置的有效性"""
        required_keys = ['model', 'dataset', 'training']
        
        for key in required_keys:
            if key not in config:
                raise ValueError(f"配置中缺少必需的键: {key}")
        
        return True

# 配置数据类
from dataclasses import dataclass
from typing import List

@dataclass
class ModelConfig:
    """模型配置"""
    type: str
    input_channels: int = 3
    output_channels: int = 3
    num_features: int = 64
    num_blocks: int = 6

@dataclass
class DatasetConfig:
    """数据集配置"""
    type: str
    root_dir: str
    train_split: str = "train"
    val_split: str = "val"
    test_split: str = "test"
    batch_size: int = 16
    num_workers: int = 4
    pin_memory: bool = True

@dataclass
class TrainingConfig:
    """训练配置"""
    epochs: int = 100
    learning_rate: float = 1e-4
    weight_decay: float = 1e-5
    gradient_clip: float = 1.0
    save_interval: int = 10
    eval_interval: int = 5

@dataclass
class ExperimentConfig:
    """实验配置"""
    name: str
    output_dir: str = "outputs"
    seed: int = 42
    device: str = "auto"
    use_wandb: bool = True
    wandb_project: str = "my-cv-project"

@dataclass
class Config:
    """主配置"""
    model: ModelConfig
    dataset: DatasetConfig
    training: TrainingConfig
    experiment: ExperimentConfig

# 注册配置类
config_manager = ConfigManager()
config_manager.register_config("config", Config)
config_manager.register_config("model", ModelConfig)
config_manager.register_config("dataset", DatasetConfig)
config_manager.register_config("training", TrainingConfig)
config_manager.register_config("experiment", ExperimentConfig)
```

### 4. 日志系统

```python
# src/my_project/utils/logger.py
"""
日志系统
"""
import sys
from pathlib import Path
from typing import Optional
from loguru import logger
import wandb

class Logger:
    """统一日志管理器"""
    
    def __init__(self, 
                 log_dir: str = "outputs/logs",
                 log_level: str = "INFO",
                 use_wandb: bool = False,
                 wandb_config: Optional[dict] = None):
        
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # 配置loguru
        logger.remove()  # 移除默认处理器
        
        # 控制台输出
        logger.add(
            sys.stdout,
            level=log_level,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                   "<level>{level: <8}</level> | "
                   "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
                   "<level>{message}</level>",
            colorize=True
        )
        
        # 文件输出
        logger.add(
            self.log_dir / "app.log",
            level=log_level,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            rotation="10 MB",
            retention="7 days"
        )
        
        # W&B集成
        self.use_wandb = use_wandb
        if use_wandb and wandb_config:
            wandb.init(**wandb_config)
    
    def info(self, message: str):
        """信息日志"""
        logger.info(message)
    
    def warning(self, message: str):
        """警告日志"""
        logger.warning(message)
    
    def error(self, message: str):
        """错误日志"""
        logger.error(message)
    
    def debug(self, message: str):
        """调试日志"""
        logger.debug(message)
    
    def log_metrics(self, metrics: dict, step: Optional[int] = None):
        """记录指标"""
        # 本地日志
        metrics_str = " | ".join([f"{k}: {v:.4f}" for k, v in metrics.items()])
        logger.info(f"Metrics - {metrics_str}")
        
        # W&B日志
        if self.use_wandb:
            wandb.log(metrics, step=step)
    
    def log_model(self, model_path: str, name: str = "model"):
        """记录模型"""
        if self.use_wandb:
            wandb.save(model_path, base_path=str(Path(model_path).parent))
    
    def finish(self):
        """结束日志记录"""
        if self.use_wandb:
            wandb.finish()
```

## 📊 数据处理系统

### 1. 基础数据集类

```python
# src/my_project/data/base_dataset.py
"""
基础数据集类
"""
import torch
from torch.utils.data import Dataset
from typing import Dict, Any, Optional, Callable
from pathlib import Path
import cv2
import numpy as np
from PIL import Image

class BaseDataset(Dataset):
    """基础数据集类"""
    
    def __init__(self, 
                 root_dir: str,
                 split: str = "train",
                 transform: Optional[Callable] = None,
                 target_transform: Optional[Callable] = None):
        
        self.root_dir = Path(root_dir)
        self.split = split
        self.transform = transform
        self.target_transform = target_transform
        
        # 子类需要实现的属性
        self.samples = []
        self.classes = []
        
        # 加载数据
        self._load_data()
    
    def _load_data(self):
        """加载数据 - 子类需要实现"""
        raise NotImplementedError("子类必须实现 _load_data 方法")
    
    def __len__(self) -> int:
        return len(self.samples)
    
    def __getitem__(self, index: int) -> Dict[str, Any]:
        """获取数据项 - 子类可以重写"""
        sample = self.samples[index]
        
        # 加载图像
        image = self._load_image(sample['image_path'])
        
        # 应用变换
        if self.transform:
            image = self.transform(image)
        
        return {
            'image': image,
            'index': index,
            'path': str(sample['image_path'])
        }
    
    def _load_image(self, image_path: str) -> np.ndarray:
        """加载图像"""
        image = cv2.imread(str(image_path))
        if image is None:
            raise ValueError(f"无法加载图像: {image_path}")
        
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        return image.astype(np.float32) / 255.0
    
    def get_class_weights(self) -> torch.Tensor:
        """计算类别权重（用于不平衡数据集）"""
        if not self.classes:
            return torch.ones(1)
        
        class_counts = torch.bincount(torch.tensor(self.classes))
        total_samples = len(self.samples)
        weights = total_samples / (len(class_counts) * class_counts.float())
        
        return weights

class PairedDataset(BaseDataset):
    """配对数据集（如图像增强任务）"""
    
    def __init__(self, 
                 root_dir: str,
                 input_dir: str = "input",
                 target_dir: str = "target",
                 split: str = "train",
                 transform: Optional[Callable] = None,
                 paired_transform: Optional[Callable] = None):
        
        self.input_dir = input_dir
        self.target_dir = target_dir
        self.paired_transform = paired_transform
        
        super().__init__(root_dir, split, transform)
    
    def _load_data(self):
        """加载配对数据"""
        input_path = self.root_dir / self.split / self.input_dir
        target_path = self.root_dir / self.split / self.target_dir
        
        if not input_path.exists() or not target_path.exists():
            raise ValueError(f"数据路径不存在: {input_path} 或 {target_path}")
        
        # 获取输入图像列表
        input_images = sorted(list(input_path.glob("*.png")) + list(input_path.glob("*.jpg")))
        
        for input_img in input_images:
            # 查找对应的目标图像
            target_img = target_path / input_img.name
            
            if target_img.exists():
                self.samples.append({
                    'input_path': input_img,
                    'target_path': target_img,
                    'image_path': input_img  # 兼容基类
                })
    
    def __getitem__(self, index: int) -> Dict[str, Any]:
        """获取配对数据项"""
        sample = self.samples[index]
        
        # 加载输入和目标图像
        input_image = self._load_image(sample['input_path'])
        target_image = self._load_image(sample['target_path'])
        
        # 应用配对变换
        if self.paired_transform:
            transformed = self.paired_transform(image=input_image, target=target_image)
            input_image = transformed['image']
            target_image = transformed['target']
        
        # 应用单独变换
        if self.transform:
            input_image = self.transform(image=input_image)['image']
            target_image = self.transform(image=target_image)['image']
        
        return {
            'input': input_image,
            'target': target_image,
            'index': index,
            'input_path': str(sample['input_path']),
            'target_path': str(sample['target_path'])
        }
```

### 2. 数据变换系统

```python
# src/my_project/data/transforms.py
"""
数据变换系统
"""
import albumentations as A
from albumentations.pytorch import ToTensorV2
import cv2
import numpy as np
from typing import Dict, Any, List, Optional

@register_transform("BasicTransform")
class BasicTransform:
    """基础变换"""
    
    def __init__(self, 
                 image_size: int = 256,
                 normalize: bool = True,
                 mean: List[float] = [0.485, 0.456, 0.406],
                 std: List[float] = [0.229, 0.224, 0.225]):
        
        transforms = [
            A.Resize(image_size, image_size),
        ]
        
        if normalize:
            transforms.append(A.Normalize(mean=mean, std=std))
        
        transforms.append(ToTensorV2())
        
        self.transform = A.Compose(transforms)
    
    def __call__(self, **kwargs) -> Dict[str, Any]:
        return self.transform(**kwargs)

@register_transform("TrainingTransform")
class TrainingTransform:
    """训练时的数据增强"""
    
    def __init__(self, 
                 image_size: int = 256,
                 flip_prob: float = 0.5,
                 rotation_limit: int = 15,
                 brightness_limit: float = 0.2,
                 contrast_limit: float = 0.2):
        
        self.transform = A.Compose([
            A.Resize(image_size, image_size),
            A.HorizontalFlip(p=flip_prob),
            A.Rotate(limit=rotation_limit, p=0.5),
            A.RandomBrightnessContrast(
                brightness_limit=brightness_limit,
                contrast_limit=contrast_limit,
                p=0.5
            ),
            A.GaussNoise(var_limit=(10.0, 50.0), p=0.3),
            A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
            ToTensorV2()
        ])
    
    def __call__(self, **kwargs) -> Dict[str, Any]:
        return self.transform(**kwargs)

@register_transform("PairedTransform")
class PairedTransform:
    """配对数据的变换（保持输入和目标的一致性）"""
    
    def __init__(self, 
                 image_size: int = 256,
                 flip_prob: float = 0.5,
                 rotation_limit: int = 15):
        
        # 几何变换（需要同时应用到输入和目标）
        self.geometric_transform = A.Compose([
            A.Resize(image_size, image_size),
            A.HorizontalFlip(p=flip_prob),
            A.Rotate(limit=rotation_limit, p=0.5),
        ], additional_targets={'target': 'image'})
        
        # 最终变换
        self.final_transform = A.Compose([
            A.Normalize(mean=[0.0, 0.0, 0.0], std=[1.0, 1.0, 1.0]),  # 不进行标准化
            ToTensorV2()
        ])
    
    def __call__(self, image: np.ndarray, target: np.ndarray) -> Dict[str, Any]:
        # 应用几何变换
        transformed = self.geometric_transform(image=image, target=target)
        
        # 分别应用最终变换
        input_final = self.final_transform(image=transformed['image'])
        target_final = self.final_transform(image=transformed['target'])
        
        return {
            'image': input_final['image'],
            'target': target_final['image']
        }

def get_transforms(transform_config: Dict[str, Any]) -> Any:
    """根据配置获取变换"""
    transform_type = transform_config.pop('type')
    return TRANSFORMS.build(transform_type, **transform_config)
```

## 🧠 模型架构系统

### 1. 示例模型实现

```python
# src/my_project/models/example_model.py
"""
示例模型实现
"""
import torch
import torch.nn as nn
from typing import List
from ..utils.registry import register_model
from .base import BaseArchitecture

@register_model("SimpleUNet")
class SimpleUNet(BaseArchitecture):
    """简单的U-Net模型示例"""

    def __init__(self,
                 input_channels: int = 3,
                 output_channels: int = 3,
                 base_channels: int = 64,
                 num_levels: int = 4):
        super().__init__()

        self.input_channels = input_channels
        self.output_channels = output_channels
        self.base_channels = base_channels
        self.num_levels = num_levels

        # 编码器
        self.encoders = nn.ModuleList()
        self.pools = nn.ModuleList()

        in_channels = input_channels
        for i in range(num_levels):
            out_channels = base_channels * (2 ** i)

            encoder = nn.Sequential(
                nn.Conv2d(in_channels, out_channels, 3, 1, 1),
                nn.BatchNorm2d(out_channels),
                nn.ReLU(inplace=True),
                nn.Conv2d(out_channels, out_channels, 3, 1, 1),
                nn.BatchNorm2d(out_channels),
                nn.ReLU(inplace=True)
            )

            self.encoders.append(encoder)

            if i < num_levels - 1:
                self.pools.append(nn.MaxPool2d(2))

            in_channels = out_channels

        # 解码器
        self.decoders = nn.ModuleList()
        self.upsamples = nn.ModuleList()

        for i in range(num_levels - 1, 0, -1):
            in_channels = base_channels * (2 ** i)
            out_channels = base_channels * (2 ** (i - 1))

            upsample = nn.ConvTranspose2d(in_channels, out_channels, 2, 2)
            self.upsamples.append(upsample)

            decoder = nn.Sequential(
                nn.Conv2d(in_channels, out_channels, 3, 1, 1),
                nn.BatchNorm2d(out_channels),
                nn.ReLU(inplace=True),
                nn.Conv2d(out_channels, out_channels, 3, 1, 1),
                nn.BatchNorm2d(out_channels),
                nn.ReLU(inplace=True)
            )

            self.decoders.append(decoder)

        # 输出层
        self.output_conv = nn.Conv2d(base_channels, output_channels, 1)
        self.output_activation = nn.Sigmoid()

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播"""
        # 编码路径
        encoder_features = []
        current = x

        for i, encoder in enumerate(self.encoders):
            current = encoder(current)
            encoder_features.append(current)

            if i < len(self.pools):
                current = self.pools[i](current)

        # 解码路径
        for i, (upsample, decoder) in enumerate(zip(self.upsamples, self.decoders)):
            current = upsample(current)

            # 跳跃连接
            skip_connection = encoder_features[-(i + 2)]
            current = torch.cat([current, skip_connection], dim=1)
            current = decoder(current)

        # 输出
        output = self.output_conv(current)
        output = self.output_activation(output)

        return output
```

### 2. 损失函数系统

```python
# src/my_project/losses/losses.py
"""
损失函数实现
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision
from typing import List, Dict
from ..utils.registry import register_loss

@register_loss("MSELoss")
class MSELoss(nn.Module):
    """均方误差损失"""

    def __init__(self, reduction: str = 'mean'):
        super().__init__()
        self.mse_loss = nn.MSELoss(reduction=reduction)

    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        return self.mse_loss(pred, target)

@register_loss("L1Loss")
class L1Loss(nn.Module):
    """L1损失"""

    def __init__(self, reduction: str = 'mean'):
        super().__init__()
        self.l1_loss = nn.L1Loss(reduction=reduction)

    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        return self.l1_loss(pred, target)

@register_loss("CombinedLoss")
class CombinedLoss(nn.Module):
    """组合损失函数"""

    def __init__(self,
                 l1_weight: float = 1.0,
                 mse_weight: float = 0.1,
                 perceptual_weight: float = 0.1):
        super().__init__()

        self.l1_weight = l1_weight
        self.mse_weight = mse_weight
        self.perceptual_weight = perceptual_weight

        self.l1_loss = nn.L1Loss()
        self.mse_loss = nn.MSELoss()

        # 感知损失（使用预训练VGG）
        if perceptual_weight > 0:
            self.perceptual_loss = PerceptualLoss()

    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """计算组合损失"""
        total_loss = 0.0

        # L1损失
        if self.l1_weight > 0:
            l1_loss = self.l1_loss(pred, target)
            total_loss += self.l1_weight * l1_loss

        # MSE损失
        if self.mse_weight > 0:
            mse_loss = self.mse_loss(pred, target)
            total_loss += self.mse_weight * mse_loss

        # 感知损失
        if self.perceptual_weight > 0:
            perceptual_loss = self.perceptual_loss(pred, target)
            total_loss += self.perceptual_weight * perceptual_loss

        return total_loss

class PerceptualLoss(nn.Module):
    """感知损失（基于VGG特征）"""

    def __init__(self, layers: List[str] = ['relu1_2', 'relu2_2', 'relu3_3']):
        super().__init__()

        # 加载预训练VGG
        vgg = torchvision.models.vgg16(pretrained=True).features
        self.vgg = vgg.eval()

        # 冻结参数
        for param in self.vgg.parameters():
            param.requires_grad = False

        self.layer_names = layers
        self.layer_indices = self._get_layer_indices()

    def _get_layer_indices(self) -> Dict[str, int]:
        """获取层索引"""
        layer_map = {
            'relu1_2': 4,
            'relu2_2': 9,
            'relu3_3': 16,
            'relu4_3': 23
        }
        return {name: layer_map[name] for name in self.layer_names}

    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """计算感知损失"""
        # 确保输入范围正确
        pred = pred * 2.0 - 1.0  # [0,1] -> [-1,1]
        target = target * 2.0 - 1.0

        # 提取特征
        pred_features = self._extract_features(pred)
        target_features = self._extract_features(target)

        # 计算特征损失
        loss = 0.0
        for name in self.layer_names:
            loss += F.mse_loss(pred_features[name], target_features[name])

        return loss / len(self.layer_names)

    def _extract_features(self, x: torch.Tensor) -> Dict[str, torch.Tensor]:
        """提取VGG特征"""
        features = {}
        current = x

        for i, layer in enumerate(self.vgg):
            current = layer(current)

            for name, idx in self.layer_indices.items():
                if i == idx:
                    features[name] = current

        return features
```

---

本指南提供了构建深度学习计算机视觉项目的完整框架。通过模块化设计和配置驱动的方法，您可以快速搭建一个可扩展、可维护的研究项目。
