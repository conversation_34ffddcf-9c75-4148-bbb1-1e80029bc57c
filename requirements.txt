# This file was autogenerated by uv via the following command:
#    uv pip compile pyproject.toml -o requirements.txt
absl-py==2.3.1
    # via tensorboard
accelerate==1.9.0
    # via llie (pyproject.toml)
albucore==0.0.24
    # via albumentations
albumentations==2.0.8
    # via llie (pyproject.toml)
annotated-types==0.7.0
    # via pydantic
antlr4-python3-runtime==4.9.3
    # via
    #   hydra-core
    #   omegaconf
anyio==4.9.0
    # via
    #   httpx
    #   jupyter-server
argon2-cffi==25.1.0
    # via jupyter-server
argon2-cffi-bindings==21.2.0
    # via argon2-cffi
arrow==1.3.0
    # via isoduration
asttokens==3.0.0
    # via stack-data
async-lru==2.0.5
    # via jupyterlab
attrs==25.3.0
    # via
    #   jsonschema
    #   referencing
babel==2.17.0
    # via jupyterlab-server
beautifulsoup4==4.13.4
    # via nbconvert
black==25.1.0
    # via llie (pyproject.toml)
bleach==6.2.0
    # via nbconvert
certifi==2025.7.14
    # via
    #   httpcore
    #   httpx
    #   requests
    #   sentry-sdk
cffi==1.17.1
    # via argon2-cffi-bindings
charset-normalizer==3.4.2
    # via requests
click==8.1.8
    # via
    #   black
    #   wandb
comm==0.2.3
    # via
    #   ipykernel
    #   ipywidgets
contourpy==1.3.0
    # via matplotlib
cycler==0.12.1
    # via matplotlib
debugpy==1.8.15
    # via ipykernel
decorator==5.2.1
    # via ipython
defusedxml==0.7.1
    # via nbconvert
einops==0.8.1
    # via llie (pyproject.toml)
eval-type-backport==0.2.2
    # via
    #   albumentations
    #   wandb
exceptiongroup==1.3.0
    # via
    #   anyio
    #   ipython
    #   pytest
executing==2.2.0
    # via stack-data
fastjsonschema==2.21.1
    # via nbformat
filelock==3.18.0
    # via
    #   huggingface-hub
    #   torch
flake8==7.3.0
    # via llie (pyproject.toml)
fonttools==4.59.0
    # via matplotlib
fqdn==1.5.1
    # via jsonschema
fsspec==2025.7.0
    # via
    #   huggingface-hub
    #   torch
gitdb==4.0.12
    # via gitpython
gitpython==3.1.45
    # via wandb
grpcio==1.74.0
    # via tensorboard
h11==0.16.0
    # via httpcore
hf-xet==1.1.5
    # via huggingface-hub
httpcore==1.0.9
    # via httpx
httpx==0.28.1
    # via jupyterlab
huggingface-hub==0.34.3
    # via
    #   accelerate
    #   timm
hydra-core==1.3.2
    # via llie (pyproject.toml)
idna==3.10
    # via
    #   anyio
    #   httpx
    #   jsonschema
    #   requests
imageio==2.37.0
    # via scikit-image
importlib-metadata==8.7.0
    # via
    #   jupyter-client
    #   jupyter-lsp
    #   jupyterlab
    #   jupyterlab-server
    #   markdown
    #   nbconvert
importlib-resources==6.5.2
    # via matplotlib
iniconfig==2.1.0
    # via pytest
ipykernel==6.30.0
    # via
    #   jupyter
    #   jupyter-console
    #   jupyterlab
ipython==8.18.1
    # via
    #   ipykernel
    #   ipywidgets
    #   jupyter-console
ipywidgets==8.1.7
    # via
    #   llie (pyproject.toml)
    #   jupyter
isoduration==20.11.0
    # via jsonschema
isort==6.0.1
    # via llie (pyproject.toml)
jedi==0.19.2
    # via ipython
jinja2==3.1.6
    # via
    #   jupyter-server
    #   jupyterlab
    #   jupyterlab-server
    #   nbconvert
    #   torch
json5==0.12.0
    # via jupyterlab-server
jsonpointer==3.0.0
    # via jsonschema
jsonschema==4.25.0
    # via
    #   jupyter-events
    #   jupyterlab-server
    #   nbformat
jsonschema-specifications==2025.4.1
    # via jsonschema
jupyter==1.1.1
    # via llie (pyproject.toml)
jupyter-client==8.6.3
    # via
    #   ipykernel
    #   jupyter-console
    #   jupyter-server
    #   nbclient
jupyter-console==6.6.3
    # via jupyter
jupyter-core==5.8.1
    # via
    #   ipykernel
    #   jupyter-client
    #   jupyter-console
    #   jupyter-server
    #   jupyterlab
    #   nbclient
    #   nbconvert
    #   nbformat
jupyter-events==0.12.0
    # via jupyter-server
jupyter-lsp==2.2.6
    # via jupyterlab
jupyter-server==2.16.0
    # via
    #   jupyter-lsp
    #   jupyterlab
    #   jupyterlab-server
    #   notebook
    #   notebook-shim
jupyter-server-terminals==0.5.3
    # via jupyter-server
jupyterlab==4.4.5
    # via
    #   jupyter
    #   notebook
jupyterlab-pygments==0.3.0
    # via nbconvert
jupyterlab-server==2.27.3
    # via
    #   jupyterlab
    #   notebook
jupyterlab-widgets==3.0.15
    # via ipywidgets
kiwisolver==1.4.7
    # via matplotlib
lark==1.2.2
    # via rfc3987-syntax
lazy-loader==0.4
    # via scikit-image
loguru==0.7.3
    # via llie (pyproject.toml)
lpips==0.1.4
    # via llie (pyproject.toml)
markdown==3.8.2
    # via tensorboard
markdown-it-py==3.0.0
    # via rich
markupsafe==3.0.2
    # via
    #   jinja2
    #   nbconvert
    #   werkzeug
matplotlib==3.9.4
    # via
    #   llie (pyproject.toml)
    #   seaborn
matplotlib-inline==0.1.7
    # via
    #   ipykernel
    #   ipython
mccabe==0.7.0
    # via flake8
mdurl==0.1.2
    # via markdown-it-py
mistune==3.1.3
    # via nbconvert
mpmath==1.3.0
    # via sympy
mypy==1.17.0
    # via llie (pyproject.toml)
mypy-extensions==1.1.0
    # via
    #   black
    #   mypy
nbclient==0.10.2
    # via nbconvert
nbconvert==7.16.6
    # via
    #   jupyter
    #   jupyter-server
nbformat==5.10.4
    # via
    #   jupyter-server
    #   nbclient
    #   nbconvert
nest-asyncio==1.6.0
    # via ipykernel
networkx==3.2.1
    # via
    #   scikit-image
    #   torch
notebook==7.4.4
    # via jupyter
notebook-shim==0.2.4
    # via
    #   jupyterlab
    #   notebook
numpy==2.0.2
    # via
    #   llie (pyproject.toml)
    #   accelerate
    #   albucore
    #   albumentations
    #   contourpy
    #   imageio
    #   lpips
    #   matplotlib
    #   opencv-python
    #   opencv-python-headless
    #   pandas
    #   scikit-image
    #   scipy
    #   seaborn
    #   tensorboard
    #   tifffile
    #   torchvision
nvidia-cublas-cu12==********
    # via
    #   nvidia-cudnn-cu12
    #   nvidia-cusolver-cu12
    #   torch
nvidia-cuda-cupti-cu12==12.6.80
    # via torch
nvidia-cuda-nvrtc-cu12==12.6.77
    # via torch
nvidia-cuda-runtime-cu12==12.6.77
    # via torch
nvidia-cudnn-cu12==********
    # via torch
nvidia-cufft-cu12==********
    # via torch
nvidia-cufile-cu12==********
    # via torch
nvidia-curand-cu12==*********
    # via torch
nvidia-cusolver-cu12==********
    # via torch
nvidia-cusparse-cu12==********
    # via
    #   nvidia-cusolver-cu12
    #   torch
nvidia-cusparselt-cu12==0.6.3
    # via torch
nvidia-nccl-cu12==2.26.2
    # via torch
nvidia-nvjitlink-cu12==12.6.85
    # via
    #   nvidia-cufft-cu12
    #   nvidia-cusolver-cu12
    #   nvidia-cusparse-cu12
    #   torch
nvidia-nvtx-cu12==12.6.77
    # via torch
omegaconf==2.3.0
    # via
    #   llie (pyproject.toml)
    #   hydra-core
opencv-python==*********
    # via llie (pyproject.toml)
opencv-python-headless==*********
    # via
    #   albucore
    #   albumentations
overrides==7.7.0
    # via jupyter-server
packaging==25.0
    # via
    #   accelerate
    #   black
    #   huggingface-hub
    #   hydra-core
    #   ipykernel
    #   jupyter-events
    #   jupyter-server
    #   jupyterlab
    #   jupyterlab-server
    #   lazy-loader
    #   matplotlib
    #   nbconvert
    #   pytest
    #   scikit-image
    #   tensorboard
    #   wandb
pandas==2.3.1
    # via
    #   llie (pyproject.toml)
    #   seaborn
pandocfilters==1.5.1
    # via nbconvert
parso==0.8.4
    # via jedi
pathspec==0.12.1
    # via
    #   black
    #   mypy
pexpect==4.9.0
    # via ipython
pillow==11.3.0
    # via
    #   llie (pyproject.toml)
    #   imageio
    #   matplotlib
    #   scikit-image
    #   tensorboard
    #   torchvision
platformdirs==4.3.8
    # via
    #   black
    #   jupyter-core
    #   wandb
pluggy==1.6.0
    # via pytest
prometheus-client==0.22.1
    # via jupyter-server
prompt-toolkit==3.0.51
    # via
    #   ipython
    #   jupyter-console
protobuf==6.31.1
    # via
    #   tensorboard
    #   wandb
psutil==7.0.0
    # via
    #   accelerate
    #   ipykernel
ptyprocess==0.7.0
    # via
    #   pexpect
    #   terminado
pure-eval==0.2.3
    # via stack-data
pycodestyle==2.14.0
    # via flake8
pycparser==2.22
    # via cffi
pydantic==2.11.7
    # via
    #   albumentations
    #   wandb
pydantic-core==2.33.2
    # via pydantic
pyflakes==3.4.0
    # via flake8
pygments==2.19.2
    # via
    #   ipython
    #   jupyter-console
    #   nbconvert
    #   pytest
    #   rich
pyparsing==3.2.3
    # via matplotlib
pytest==8.4.1
    # via llie (pyproject.toml)
python-dateutil==2.9.0.post0
    # via
    #   arrow
    #   jupyter-client
    #   matplotlib
    #   pandas
python-json-logger==3.3.0
    # via jupyter-events
pytorch-msssim==1.0.0
    # via llie (pyproject.toml)
pytz==2025.2
    # via pandas
pyyaml==6.0.2
    # via
    #   llie (pyproject.toml)
    #   accelerate
    #   albumentations
    #   huggingface-hub
    #   jupyter-events
    #   omegaconf
    #   timm
    #   wandb
pyzmq==27.0.0
    # via
    #   ipykernel
    #   jupyter-client
    #   jupyter-console
    #   jupyter-server
referencing==0.36.2
    # via
    #   jsonschema
    #   jsonschema-specifications
    #   jupyter-events
requests==2.32.4
    # via
    #   huggingface-hub
    #   jupyterlab-server
    #   wandb
rfc3339-validator==0.1.4
    # via
    #   jsonschema
    #   jupyter-events
rfc3986-validator==0.1.1
    # via
    #   jsonschema
    #   jupyter-events
rfc3987-syntax==1.1.0
    # via jsonschema
rich==14.1.0
    # via llie (pyproject.toml)
rpds-py==0.26.0
    # via
    #   jsonschema
    #   referencing
safetensors==0.5.3
    # via
    #   accelerate
    #   timm
scikit-image==0.24.0
    # via llie (pyproject.toml)
scipy==1.13.1
    # via
    #   llie (pyproject.toml)
    #   albumentations
    #   lpips
    #   scikit-image
seaborn==0.13.2
    # via llie (pyproject.toml)
send2trash==1.8.3
    # via jupyter-server
sentry-sdk==2.34.0
    # via wandb
setuptools==80.9.0
    # via
    #   jupyterlab
    #   tensorboard
    #   triton
simsimd==6.5.0
    # via albucore
six==1.17.0
    # via
    #   python-dateutil
    #   rfc3339-validator
smmap==5.0.2
    # via gitdb
sniffio==1.3.1
    # via anyio
soupsieve==2.7
    # via beautifulsoup4
stack-data==0.6.3
    # via ipython
stringzilla==3.12.5
    # via albucore
sympy==1.14.0
    # via torch
tensorboard==2.20.0
    # via llie (pyproject.toml)
tensorboard-data-server==0.7.2
    # via tensorboard
terminado==0.18.1
    # via
    #   jupyter-server
    #   jupyter-server-terminals
thop==0.1.1.post2209072238
    # via llie (pyproject.toml)
tifffile==2024.8.30
    # via scikit-image
timm==1.0.19
    # via llie (pyproject.toml)
tinycss2==1.4.0
    # via bleach
tomli==2.2.1
    # via
    #   black
    #   jupyterlab
    #   mypy
    #   pytest
torch==2.7.1
    # via
    #   llie (pyproject.toml)
    #   accelerate
    #   lpips
    #   pytorch-msssim
    #   thop
    #   timm
    #   torchvision
torchvision==0.22.1
    # via
    #   llie (pyproject.toml)
    #   lpips
    #   timm
tornado==6.5.1
    # via
    #   ipykernel
    #   jupyter-client
    #   jupyter-server
    #   jupyterlab
    #   notebook
    #   terminado
tqdm==4.67.1
    # via
    #   llie (pyproject.toml)
    #   huggingface-hub
    #   lpips
traitlets==5.14.3
    # via
    #   ipykernel
    #   ipython
    #   ipywidgets
    #   jupyter-client
    #   jupyter-console
    #   jupyter-core
    #   jupyter-events
    #   jupyter-server
    #   jupyterlab
    #   matplotlib-inline
    #   nbclient
    #   nbconvert
    #   nbformat
triton==3.3.1
    # via torch
types-python-dateutil==2.9.0.20250708
    # via arrow
typing-extensions==4.14.1
    # via
    #   albucore
    #   albumentations
    #   anyio
    #   async-lru
    #   beautifulsoup4
    #   black
    #   exceptiongroup
    #   gitpython
    #   huggingface-hub
    #   ipython
    #   mistune
    #   mypy
    #   pydantic
    #   pydantic-core
    #   python-json-logger
    #   referencing
    #   torch
    #   typing-inspection
    #   wandb
typing-inspection==0.4.1
    # via pydantic
tzdata==2025.2
    # via pandas
uri-template==1.3.0
    # via jsonschema
urllib3==2.5.0
    # via
    #   requests
    #   sentry-sdk
wandb==0.21.0
    # via llie (pyproject.toml)
wcwidth==0.2.13
    # via prompt-toolkit
webcolors==24.11.1
    # via jsonschema
webencodings==0.5.1
    # via
    #   bleach
    #   tinycss2
websocket-client==1.8.0
    # via jupyter-server
werkzeug==3.1.3
    # via tensorboard
widgetsnbextension==4.0.14
    # via ipywidgets
zipp==3.23.0
    # via
    #   importlib-metadata
    #   importlib-resources
