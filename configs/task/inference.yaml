# 推理任务配置文件
# 定义模型推理的相关设置

task: inference                   # 任务类型：推理模式

# ==================== 推理设置 ====================
inference:
  # 模型检查点路径（必须指定）
  checkpoint_path: ???            # 训练好的模型权重文件路径
                                 # 使用方法：python run.py task=inference inference.checkpoint_path=path/to/model.pth

  # 输入路径（必须指定）
  input_path: ???                 # 输入图像或图像目录路径
                                 # 支持单张图像或包含多张图像的目录

  # 输出目录
  output_dir: ${hydra:run.dir}/inference_results  # 推理结果保存目录

  # 推理参数
  device: cuda                    # 推理设备：cuda/cpu
  batch_size: 1                   # 推理批次大小

  # 图像处理设置
  resize_input: false             # 是否调整输入图像尺寸
  target_size: [512, 512]        # 目标尺寸（当resize_input=true时使用）

  # 输出设置
  save_format: "png"              # 输出图像格式：png/jpg
  quality: 95                     # 图像质量（对jpg格式有效）

  # 后处理设置
  apply_gamma_correction: false   # 是否应用伽马校正
  gamma: 2.2                     # 伽马值

  # 可视化设置
  create_comparison: true         # 创建对比图像（原图-增强图）
  save_individual: true           # 是否单独保存增强后的图像
