# 评估任务配置文件
# 定义模型评估的相关设置

task: evaluate                    # 任务类型：评估模式

# ==================== 评估设置 ====================
evaluation:
  # 模型检查点路径（必须指定）
  checkpoint_path: ???            # 训练好的模型权重文件路径
                                 # 使用方法：python run.py task=evaluate evaluation.checkpoint_path=path/to/model.pth

  # 输出目录
  output_dir: ${hydra:run.dir}/evaluation_results  # 评估结果保存目录

  # 评估参数
  batch_size: 1                   # 评估批次大小（通常设为1）

  # 结果保存设置
  save_images: true               # 是否保存增强后的图像
  save_metrics: true              # 是否保存详细的指标结果

  # 评估指标设置
  metrics:
    - psnr                        # 峰值信噪比
    - ssim                        # 结构相似性指数
    - mae                         # 平均绝对误差
    - lpips                       # 感知损失（可选）

  # 可视化设置
  create_comparison: true         # 创建对比图像（原图-增强图-真实图）
  comparison_format: "grid"       # 对比图格式：grid/side_by_side
